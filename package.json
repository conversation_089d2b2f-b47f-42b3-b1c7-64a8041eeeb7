{"name": "EHS", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^13.0.0", "@angular/cdk": "^13.0.0", "@angular/common": "~13.2.2", "@angular/core": "^13.4.0", "@angular/forms": "~13.2.2", "@angular/platform-browser": "~13.2.2", "@angular/platform-browser-dynamic": "^13.2.7", "@angular/router": "~13.2.2", "@awesome-cordova-plugins/core": "^5.43.0", "@awesome-cordova-plugins/unvired-cordova-sdk": "^5.43.0", "@ionic/angular": "^6.0.0", "@ionic/cordova-builders": "^6.1.0", "@ng-idle/core": "^11.1.0", "@ng-idle/keepalive": "^11.0.3", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "angular2-moment": "^1.9.0", "date-fns": "^2.28.0", "grunt": "^1.5.3", "grunt-contrib-nodeunit": "^4.0.0", "moment": "^2.30.1", "ngx-dropzone": "^3.1.0", "primeflex": "^3.3.0", "primeicons": "^6.0.1", "primeng": "^13.4.1", "rxjs": "~6.6.0", "tslib": "^2.2.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.2.3", "@angular-eslint/builder": "~13.0.1", "@angular-eslint/eslint-plugin": "~13.0.1", "@angular-eslint/eslint-plugin-template": "~13.0.1", "@angular-eslint/template-parser": "~13.0.1", "@angular/cli": "~13.2.3", "@angular/compiler": "~13.2.2", "@angular/compiler-cli": "~13.2.2", "@angular/language-service": "~13.2.2", "@ionic/angular-toolkit": "^6.0.0", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "cordova-browser": "^6.0.0", "cordova-plugin-device": "^2.0.2", "cordova-plugin-ionic-keyboard": "^2.2.0", "cordova-plugin-ionic-webview": "^5.0.0", "cordova-plugin-splashscreen": "5.0.2", "cordova-plugin-statusbar": "^2.4.2", "cordova-plugin-unvired-sdk": "^3.0.369", "eslint": "^7.6.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~3.8.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "typescript": "~4.4.4"}, "description": "An Ionic project", "cordova": {"plugins": {"cordova-plugin-statusbar": {}, "cordova-plugin-device": {}, "cordova-plugin-ionic-webview": {}, "cordova-plugin-ionic-keyboard": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-unvired-sdk": {}}, "platforms": ["browser"]}, "volta": {"node": "18.13.0"}}