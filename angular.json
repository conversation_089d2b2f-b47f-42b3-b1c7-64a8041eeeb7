{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "defaultProject": "app", "newProjectRoot": "projects", "projects": {"app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "www", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["src/theme/variables.scss", "src/global.scss", "node_modules/primeicons/primeicons.css", "node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css"], "scripts": [], "aot": false, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "ci": {"progress": false}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}, "ci": {"progress": false}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "styles": [], "scripts": [], "assets": [{"glob": "favicon.ico", "input": "src/", "output": "/"}, {"glob": "**/*", "input": "src/assets", "output": "/assets"}]}, "configurations": {"ci": {"progress": false, "watch": false}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "app:serve"}, "configurations": {"production": {"devServerTarget": "app:serve:production"}, "ci": {"devServerTarget": "app:serve:ci"}}}, "ionic-cordova-serve": {"builder": "@ionic/cordova-builders:cordova-serve", "options": {"cordovaBuildTarget": "app:ionic-cordova-build", "devServerTarget": "app:serve"}, "configurations": {"production": {"cordovaBuildTarget": "app:ionic-cordova-build:production", "devServerTarget": "app:serve:production"}}}, "ionic-cordova-build": {"builder": "@ionic/cordova-builders:cordova-build", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}}}}}}, "cli": {"analytics": false, "defaultCollection": "@ionic/angular-toolkit"}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}