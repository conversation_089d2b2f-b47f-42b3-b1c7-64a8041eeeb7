
/* This file generates files for i18n module. 
   Use the command $ grunt to generate <localization>.json files. 
*/
module.exports = function (grunt) {

    var path = require("path");
    var utils = require("./Utils");

    var AngularTranslateParser = (function () {
        function AngularTranslateParser() {
        }

        /* Sample HTML String: {{ "i18 example" | translate }}. Can be with or without spaces. */
        AngularTranslateParser.prototype.getRegexpList = function () {

            // |regexString| is the common matching pattern for HTML and Typescript translation.
            // Use 'Find & Replace' to replace this pattern in the actual regular expression array which is returned.
            var regexString = "a-zA-Z0-9._(){}\-+=\[\]|'',><&\*%$#@!`;:?~%^’–\/";

            return [/\{\{[ ]*["'`]\s*([a-zA-Z0-9._(){}\-+=\[\]|'',><&\*%$#@!`;:?~%^’–\ ]+[a-zA-Z0-9._(){}\-+=\[\]|'',><&\*%$#@!`;:?~%^’–\/])\s*["'`][ ]*\|\s*translate\s*\}\}/g, 
                    /translate\.instant\(*["'`]\s*([a-zA-Z0-9._(){}\-+=\[\]|'',><&\*%$#@!`;:?~%^’–\/ ]+[a-zA-Z0-9._(){}\-+=\[\]|'',><&\*%$#@!`;:?~%^’–\/])\s*["'`][ ]*\)/g];
        };
        AngularTranslateParser.prototype.parseMatch = function (match, regExp) {
            var text = utils.escapeLiteral(match[1]);
            if (text === false)
                text = match[1];
            return { msgid: text, msgstr: text, msgid_plural: null, msgctxt: null, line: null };
        };
        AngularTranslateParser._asd = "";
        return AngularTranslateParser;
    })();

    grunt.initConfig({

        translate_extract: {
            options: {
                output: ['en.json', 'fr.json', 'ro.json'],
                outputDir: "./src/assets/i18n",
                // basePath: optional //Just in case the basePath is not the root of the project. 
                builtInParser: "angularTranslate",
                customParser: new AngularTranslateParser(),
                errorOnDuplicatedKeys: false
            },
            files: {
                src: ["src/**/*.html", "src/**/*.ts"]
            }
        }
    });

    grunt.loadNpmTasks('grunt-translate-extract');

     // Default task(s).
  grunt.registerTask('default', ['translate_extract']);
};