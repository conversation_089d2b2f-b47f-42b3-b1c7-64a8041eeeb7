{"Accident Location": "Locatia Incident", "Accident Location is required": "Este necesara introducerea Locatia Incident", "Are you sure you want to logout?": "Sunteti sigur ca vreti sa iesiti?", "Attachments": "Atasamente", "Cancel": "<PERSON><PERSON><PERSON>", "Click to browse and add": "Click pt. cautare si adaugare", "Click to select an Employee": "Click pt. selectare angajat", "Confirmation": "Confirmare", "Contractor": "Contractor", "CONTRACTOR": "CONTRACTOR", "Conversation": "Conversa<PERSON>ie", "Conversation Topic": "Subiectul discutiei", "Conversation Topic is required": "Este necesara introducerea subiectului de discutie", "Conversation Type": "Tipul de conversatie", "Conversation Type is required": "Este necesara introducerea Tipul de conversatie", "created successfully in SAP.": "Creata cu succes in SAP", "Date and Time of Incident": "Data si ora incidentului", "Date and Time of Incident is required.": "Este necesara introducerea datei si a orei incidentului", "Date and Time of Observation": "Data si ora la care a avut loc observatia", "Date and Time of Observation is required.": "Este necesara introducerea datei si a orei la care a avut loc observatia", "Date and Time of One on One": "Data si ora discutiei Conversaţie", "Detailed Safety Conversation": "Conversatie Safety detaliata", "Details of the Incident": "Detalii despre Observație", "Details of the Incident is required.": "<PERSON><PERSON><PERSON> despre incident", "Details of the Observation": "Este necesara introducerea detaliilor despre incident", "Drop files here": "Atasati fisierele aici", "Employee": "<PERSON><PERSON><PERSON>", "Employee Number is required.": "Este necesara introducerea numarului de angajat (marca)", "Environment Health & Safety Application": "Aplicatie pentru Sanatatea si Securitatea in munca", "First Name": "Prenume", "For Wild card search - Enter ‘*’ at the end of the text.": "<PERSON>tru c<PERSON><PERSON><PERSON> anga<PERSON> - <PERSON><PERSON><PERSON> '*' la sfârșitul textului.", "Functional Location": "Locatia functionala", "Incident ": "Incident", "Incident Type": "Tipul incidentului", "Incident Type is required.": "Este necesara introducerea tipului de incident", "Info": "Info", "Initiator": "Initiator", "Initiator is required.": "Este necesara introducerea numelui initiatorului", "Injured Body Part": "Parte a corpului", "Injury": "<PERSON><PERSON><PERSON><PERSON>", "Injury Incident": "<PERSON> de văt<PERSON>mare", "is required.": "<PERSON>ste necesar.", "Language": "Limba", "Last Name": "Nume", "Login": "<PERSON><PERSON><PERSON>", "N/A": "N/A", "No": "<PERSON>u", "No Employee Details Found.": "Nu s-au gasit detalii despre angajat", "Observation": "Observație", "Observation Summary": "Sumarul observatiei", "Observation Summary is required.": "Este necesara introducerea sumarului observatiei", "Observer": "Observator", "Observer is required.": "Este necesara introducerea numelui observatorului", "Ok": "Ok", "OR": "Sau", "Or": "Sau", "Password": "Pa<PERSON><PERSON>", "Password is required.": "Este necesara introducerea parolei", "Plant": "Fabrica", "Please check internet connection and try again!": "Va rugam verificati conexiunea la internet si incercati din nou!", "Please wait...": "Va rugam asteptati...", "Potential Injured Body Part": "Parte a corpului cu potențială vătămare", "Potential Type of Injury": "Tip potențial de leziune", "Property damage": "Pagube materiale", "Property Damage Incident": "Incident de materiale pagub<PERSON>", "Safe": "In siguranta", "Safe Percentage": "Procent Safe", "Safety Concern": "Ingrijorare pe parte de Safety", "Safety Conversation": "Conversatie de Safety", "Safety Conversation is required.": "Este necesara introducerea Conversatiei de Safety", "Safety Cues": "Indicatii de Safety", "SAP User Id": "User SAP", "SAP User Id is required.": "Este necesar introducerea unui nume de utilizator", "Search": "Cautare", "Select an Incident Type": "Selectati un tip de incident", "Select Conversation Topic": "Selectati subiectul discutiei", "Select Conversation Type": "Selectati tipul de conversatie", "Select Employee": "Selectati numele angajatului", "Select Language": "Selectati limba", "Select One": "Selectati o varianta", "Select Plant": "Selectati fabrica", "Select the Accident Location": "Selectati locatia Incident", "Select the Functional Location": "Selectati locatia functionala", "Select the task": "Selectati sarcina indeplinita", "Select the Type of Treatment/Immediate Action": "Selectati tipul de tratament/Actiuni immediate", "Select Work Area": "Selectati zona de lucru", "Select Work Area of the Incident": "Selectati zona de lucru in care s-a intamplat incidentul", "Short Description": "<PERSON><PERSON><PERSON> descriere", "Significant Activity": "Activitate semnificativa", "Significant Activity is required.": "Este necesara introducearea activitatii", "Something went wrong while login, please try again!": "Ceva s-a intamplat in timpul logarii, va rugam incercati din nou!", "Submit": "Trimite", "Task": "<PERSON><PERSON><PERSON> indeplinita", "Temp": "Temporar", "TEMP": "TEMPORAR", "Type of Injury": "Tipul de accidentare", "Type of Treatment/Immediate Action": "Tipul de tratament/Actiuni immediate", "Unsaved changes will be lost. Continue?": "Modificarile nesalvate vor fi pierdute. Continuati?", "Warning": "<PERSON><PERSON><PERSON>", "Work Area": "Zona de lucru", "Work Area is required.": "Este necesara introducerea zonei de lucru", "Work Area of the Incident": "Zona de lucru in care s-a intamplat incidentul", "Work Area of the Incident is required.": "Este necesara introducerea zonei de lucru in care a avut loc incidentul", "Yes": "Da", "Is this safety item completed": "Observatiea de safety este inchisa", "Cause": "Cauza", "Select Cause": "Selectati cauza", "Cause Description": "<PERSON><PERSON><PERSON><PERSON> ca<PERSON>i", "Has the Observation been Closed out?": "Observatia a fost inchisa?", "Environmental Cause": "Cauza mediu", "Does this task require a risk assessment or SOP?": "Pentru aceasta actiune este necesara o evaluare a riscului sau a procedurilor?", "Environmental Incident": "Incident de mediu", "Safety item completed is required": "Selectati statusul observatiei", "I/A no": "I/A nu", "Accident Type": "Tipul incidentului", "Event Date": "Data evenimentului", "Workarea": "Zona de lucru", "Employee Name": "<PERSON><PERSON> anga<PERSON>", "Reported By": "Raportat de", "Execution Date": "Data <PERSON>ii", "Update": "Actualizare", "Observation been Closed out is requred?": "Selectati daca observatia a fost inchisa", "Incident Log Monitor": "Monitorizare jurnal incidente", "Clear": "<PERSON><PERSON>", "Environment Health and Safety(EHS) Entry Portal": "Portalul de intrare pentru sănătatea și siguranța mediului (EHS).", "Upload Attachments": "Încărcați atașamente", "Camera": "Aparat foto", "Remove": "Elimina", "No images have been captured": "Nu au fost capturate imagini"}