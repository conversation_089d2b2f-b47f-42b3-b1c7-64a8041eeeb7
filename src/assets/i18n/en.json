{"Accident Location": "Incident Location", "Accident Location is required": "Incident Location is required", "Are you sure you want to logout?": "Are you sure you want to logout?", "Attachments": "Attachments", "Cancel": "Cancel", "Click to browse and add": "Click to browse and add", "Click to select an Employee": "Click to select an Employee", "Confirmation": "Confirmation", "Contractor": "Contractor", "CONTRACTOR": "CONTRACTOR", "Conversation": "Conversation", "Conversation Topic": "Conversation Topic", "Conversation Topic is required": "Conversation Topic is required", "Conversation Type": "Conversation Type", "Conversation Type is required": "Conversation Type is required", "created successfully in SAP.": "Created successfully in SAP.", "Date and Time of Incident": "Date and Time of Incident", "Date and Time of Incident is required.": "Date and Time of Incident is required.", "Date and Time of Observation": "Date and Time of Near Miss / Observation", "Date and Time of Observation is required.": "Date and Time of Near Miss / Observation is required.", "Date and Time of One on One": "Date and Time of Conversation", "Detailed Safety Conversation": "Detailed Safety Conversation", "Details of the Observation": "Details of the Near Miss / Observation", "Details of the Incident": "Details of the Incident", "Details of the Incident is required.": "Details of the Incident is required.", "Drop files here": "Drop files here", "Employee": "Employee", "Employee Number is required.": "Employee Number is required.", "Environment Health & Safety Application": "Environmental, Health and Safety (EHS) Entry Portal", "First Name": "First Name", "For Wild card search - Enter ‘*’ at the end of the text.": "For employee search - enter “*” at the end of your text. Click search", "Functional Location": "Functional Location", "Incident ": "Incident ", "Incident Type": "Incident Type", "Incident Type is required.": "Incident Type is required.", "Info": "Info", "Initiator": "Initiator", "Initiator is required.": "Initiator is required.", "Injured Body Part": "Injured Body Part", "Injury": "Injury", "Injury Incident": "Injury Incident", "is required.": "is required.", "Language": "Language", "Last Name": "Last Name", "Login": "<PERSON><PERSON>", "N/A": "N/A", "No": "No", "No Employee Details Found.": "No Employee Details Found.", "Observation": "Observation", "Observation Summary": "Observation Summary", "Observation Summary is required.": "Observation Summary is required.", "Observer": "Observer", "Observer is required.": "Observer is required.", "Ok": "Ok", "OR": "OR", "Or": "Or", "Password": "Password", "Password is required.": "Password is required.", "Plant": "Plant", "Please check internet connection and try again!": "Please check internet connection and try again!", "Please wait...": "Please wait...", "Potential Injured Body Part": "Potential Injured Body Part", "Potential Type of Injury": "Potential Type of Injury", "Property damage": "Property Damage", "Property Damage Incident": "Property Damage Incident", "Safe": "Safe", "Safe Percentage": "Safe Percentage", "Safety Concern": "Safety Concern", "Safety Conversation": "Safety Conversation", "Safety Conversation is required.": "Safety Conversation is required.", "Safety Cues": "Safety Cues", "SAP User Id": "SAP User Id", "SAP User Id is required.": "SAP User Id is required.", "Search": "Search", "Select an Incident Type": "Select an Incident Type", "Select Conversation Topic": "Select Conversation Topic", "Select Conversation Type": "Select Conversation Type", "Select Employee": "Select Employee", "Select Language": "Select Language", "Select One": "Select One", "Select Plant": "Select Plant", "Select the Accident Location": "Select the Incident Location", "Select the Functional Location": "Select the Functional Location", "Select the task": "Select the task", "Select the Type of Treatment/Immediate Action": "Select the Type of Treatment/Immediate Action", "Select Work Area": "Select Work Area", "Select Work Area of the Incident": "Select Work Area of the Incident", "Short Description": "Short Description", "Significant Activity": "Significant Activity", "Significant Activity is required.": "Significant Activity is required.", "Something went wrong while login, please try again!": "Something went wrong while login, please try again!", "Submit": "Submit", "Task": "Task", "Temp": "Temp", "TEMP": "TEMP", "Type of Injury": "Type of Injury", "Type of Treatment/Immediate Action": "Type of Treatment/Immediate Action", "Unsaved changes will be lost. Continue?": "Unsaved changes will be lost. Continue?", "Warning": "Warning", "Work Area": "Work Area", "Work Area is required.": "Work Area is required.", "Work Area of the Incident": "Work Area of the Incident", "Work Area of the Incident is required.": "Work Area of the Incident is required.", "Yes": "Yes", "Is this safety item completed": "Is this safety item completed", "Cause": "Cause", "Select Cause": "Select Cause", "Cause Description": "Cause Description", "Has the Observation been Closed out?": "Has the Near Miss / Observation been Closed out?", "Environmental Cause": "Environmental Cause", "Does this task require a risk assessment or SOP?": "Does this task require a risk assessment or SOP?", "Environmental Incident": "Environmental Incident", "Safety item completed is required": "Safety item completed is required.", "I/A no": "I/A no", "Accident Type": "Accident Type", "Event Date": "Event Date", "Workarea": "Workarea", "Employee Name": "Employee Name", "Reported By": "Reported By", "Execution Date": "Execution Date", "Update": "Update", "Observation been Closed out is requred?": "Near Miss / Observation been Closed out is requred.", "Environmental incident": "Environmental incident", "Incident Location": "Incident Location", "Near Miss / Observation": "Near Miss / Observation", "Incident Log Monitor": "Incident Log Monitor", "Near Miss": "Near Miss", "Details of the Near Miss": "Details of the Near Miss", "Environment Health and Safety(EHS) Entry Portal": "Environment Health and Safety(EHS) Entry Portal", "Upload Attachments": "Upload Attachments", "Camera": "Camera", "Remove": "Remove", "Clear": "Clear", "Corrective/ Preventative Action Taken": "Corrective/ Preventative Action Taken", "No images have been captured": "No images have been captured", "Session Expired": "Session Expired", "Your session has expired due to inactivity": "Your session has expired due to inactivity", "Session Expiring Soon": "Session Expiring Soon", "Your session is about to expire due to inactivity. To continue, please click Extend Session": "Your session is about to expire due to inactivity. To continue, please click Extend Session", "Extend Session": "Extend Session"}