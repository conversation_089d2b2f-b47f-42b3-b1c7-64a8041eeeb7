{"Accident Location": "<PERSON><PERSON>", "Accident Location is required": "<PERSON><PERSON>", "Are you sure you want to logout?": "<PERSON><PERSON><PERSON>u kapatmak istediğinizden emin misiniz?", "Attachments": "Ekler", "Cancel": "İptal", "Click to browse and add": "Göz atmak ve eklemek için tıklayın", "Click to select an Employee": "Bir Çalışan seçmek için tıklayın", "Confirmation": "<PERSON><PERSON><PERSON>", "Contractor": "<PERSON><PERSON><PERSON>ah<PERSON> (Taşeron)", "CONTRACTOR": "<PERSON><PERSON><PERSON>ah<PERSON> (Taşeron)", "Conversation": "<PERSON><PERSON><PERSON><PERSON>", "Conversation Topic": "<PERSON><PERSON><PERSON><PERSON>", "Conversation Topic is required": "<PERSON><PERSON>ş<PERSON>", "Conversation Type": "Konuşma Türü", "Conversation Type is required": "Gör<PERSON>şme Türü g<PERSON>klid<PERSON>", "created successfully in SAP.": "SAP'de başarıyla oluşturuldu.", "Date and Time of Incident": "<PERSON><PERSON>", "Date and Time of Incident is required.": "<PERSON><PERSON> ve <PERSON> gere<PERSON>lid<PERSON>.", "Date and Time of Observation": "<PERSON><PERSON><PERSON><PERSON>", "Date and Time of Observation is required.": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON> gereklidir.", "Date and Time of One on One": "<PERSON><PERSON> <PERSON><PERSON> ve <PERSON>", "Detailed Safety Conversation": "Ayrıntılı Güvenlik Görüşmesi", "Details of the Incident": "<PERSON><PERSON><PERSON><PERSON>", "Details of the Incident is required.": "<PERSON><PERSON>la ilgili ayrıntılar gereklidir.", "Details of the Observation": "<PERSON><PERSON><PERSON><PERSON>", "Drop files here": "Dosyaları buraya bırak", "Employee": "Çalışan", "Employee Number is required.": "Çalışan Numarası gereklidir.", "Environment Health & Safety Application": "Çevre, Sağlık ve Güvenlik Uygulaması", "First Name": "İlk adı", "For Wild card search - Enter ‘*’ at the end of the text.": "Çalışan araması için - metninizin sonuna '*' girin. Ara'yı tıklayın.", "Functional Location": "İşlevsel Konum", "Incident ": "<PERSON><PERSON>", "Incident Type": "<PERSON><PERSON>", "Incident Type is required.": "<PERSON><PERSON>.", "Info": "<PERSON><PERSON><PERSON>", "Initiator": "başlatıcı", "Initiator is required.": "Başlatıcı gereklidir.", "Injured Body Part": "Yaralı Vücut Parçası", "Injury": "<PERSON><PERSON><PERSON><PERSON>", "Injury Incident": "Yaralanma Olayı", "is required.": "gereklidir.", "Language": "Dil", "Last Name": "<PERSON>y isim", "Login": "<PERSON><PERSON><PERSON>", "N/A": "Uygulanamaz", "No": "Hay<PERSON><PERSON>", "No Employee Details Found.": "Çalışan Detayı Bulunamadı.", "Observation": "<PERSON><PERSON><PERSON><PERSON>", "Observation Summary": "<PERSON><PERSON><PERSON>m Özeti", "Observation Summary is required.": "Gözlem Özeti gereklidir.", "Observer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Observer is required.": "Gözlemci gereklidir.", "Ok": "<PERSON><PERSON>", "OR": "VEYA", "Or": "<PERSON><PERSON><PERSON>", "Password": "Şifre", "Password is required.": "<PERSON><PERSON><PERSON>.", "Plant": "<PERSON><PERSON> ", "Please check internet connection and try again!": "Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin!", "Please wait...": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>in...", "Potential Injured Body Part": "Potansiyel Yaralanmış Vücut Parçası", "Potential Type of Injury": "<PERSON><PERSON><PERSON><PERSON><PERSON>ü<PERSON>", "Property damage": "<PERSON><PERSON>", "Property Damage Incident": "<PERSON><PERSON>", "Safe": "<PERSON><PERSON><PERSON><PERSON>", "Safe Percentage": "<PERSON><PERSON><PERSON><PERSON>", "Safety Concern": "Güvenlik endişesi", "Safety Conversation": "Güvenlik Sohbeti", "Safety Conversation is required.": "Güvenlik Görüşmesi gereklidir.", "Safety Cues": "Güvenlik İpuçları", "SAP User Id": "SAP Kullanıcı Kimliği", "SAP User Id is required.": "SAP Kullanıcı Kimliği gerekli.", "Search": "<PERSON><PERSON>", "Select an Incident Type": "Bir <PERSON><PERSON>ü<PERSON>", "Select Conversation Topic": "<PERSON><PERSON><PERSON><PERSON>", "Select Conversation Type": "Konuşma Türünü Seçin", "Select Employee": "Çalışan <PERSON>", "Select Language": "<PERSON><PERSON>", "Select One": "<PERSON><PERSON><PERSON>", "Select Plant": "<PERSON><PERSON>", "Select the Accident Location": "<PERSON><PERSON>", "Select the Functional Location": "İşlem Yerini <PERSON>", "Select the task": "<PERSON><PERSON><PERSON><PERSON>", "Select the Type of Treatment/Immediate Action": "<PERSON><PERSON>/Acil İşlemi Seçin", "Select Work Area": "Çalışma Alanı Seçin", "Select Work Area of the Incident": "Olayın <PERSON>ışma Alanını Seçin", "Short Description": "<PERSON><PERSON><PERSON>", "Significant Activity": "Öne<PERSON><PERSON>", "Significant Activity is required.": "Önemli Etkinlik gereklidir.", "Something went wrong while login, please try again!": "Oturum açarken bir so<PERSON>, lü<PERSON><PERSON> tekrar den<PERSON>!", "Submit": "Göndermek", "Task": "<PERSON><PERSON><PERSON><PERSON>", "Temp": "Geçici süreli", "TEMP": "GEÇİCİ SÜRELİ", "Type of Injury": "Yaralanma Türü", "Type of Treatment/Immediate Action": "<PERSON><PERSON>/<PERSON><PERSON><PERSON>", "Unsaved changes will be lost. Continue?": "Kaydedilmemiş değişiklikler kaybolacak. Devam et?", "Warning": "Uyarı", "Work Area": "Çalışma alanı", "Work Area is required.": "Çalışma Alanı gereklidir.", "Work Area of the Incident": "<PERSON>layın <PERSON>", "Work Area of the Incident is required.": "Olayın <PERSON>ışma Alanı gereklidir.", "Yes": "<PERSON><PERSON>", "Is this safety item completed": "Bu güvenlik öğesi tama<PERSON>landı mı?", "Cause": "Sebep", "Select Cause": "<PERSON><PERSON><PERSON>", "Cause Description": "Sebep A<PERSON>ı<PERSON>ı", "Has the Observation been Closed out?": "<PERSON><PERSON><PERSON>m Kapatıldı mı?", "Environmental Cause": "<PERSON><PERSON><PERSON><PERSON>", "Does this task require a risk assessment or SOP?": "Bu görev bir risk değerlendirmesi veya SOP gerektiriyor mu?", "Environmental Incident": "<PERSON><PERSON><PERSON><PERSON>", "Safety item completed is required": "Tamamlanan güvenlik öğesi gereklidir.", "I/A no": "Olay/Kaza Sayısı", "Accident Type": "<PERSON><PERSON>", "Event Date": "<PERSON><PERSON>", "Workarea": "Çalışma alanı", "Employee Name": "Çalışan Adı", "Reported By": "Raporlay<PERSON>", "Execution Date": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Observation been Closed out is requred?": "<PERSON><PERSON><PERSON><PERSON> kapatılması gereklidir.", "Incident Log Monitor": "<PERSON><PERSON>", "Clear": "<PERSON><PERSON><PERSON>", "Environment Health and Safety(EHS) Entry Portal": "Çevre Sağlığı ve Güvenliği (EHS) Giriş Portalı", "Upload Attachments": "<PERSON><PERSON><PERSON>", "Camera": "<PERSON><PERSON><PERSON>", "Remove": "Kaldır", "No images have been captured": "<PERSON>ç görüntü alınmadı", "Session Expiring Soon": "Oturum Yakında Sona Erecek", "Your session is about to expire due to inactivity. To continue, please click Extend Session": "Oturumunuz hareketsizlik nedeniyle sona ermek üzeredir. Devam etmek için lütfen Oturumu Sürdür’e tıklayın", "Extend Session": "<PERSON><PERSON><PERSON><PERSON>", "Session Expired": "Oturum Sona Erdi", "Your session has expired due to inactivity": "Oturumunuz hareketsizlik nedeniyle sona erdi"}