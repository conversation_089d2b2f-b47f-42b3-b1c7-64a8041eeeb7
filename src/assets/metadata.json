{"name": "EHS", "description": "Environment Health & Saftey Application", "version": "1.0", "ACC_LOCS": {"description": "Accident locations", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ACC_LOCS_HEADER": {"className": "com.plastipak.ehs.be.ACC_LOCS_HEADER", "header": true, "field": [{"name": "PLANT", "description": "Plant", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "WORKAREA", "description": "Work Area or Pattern", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "RECNROOT", "description": "Sequential Number of Data Record", "isGid": true, "length": "20", "mandatory": true, "sqlType": "INTEGER"}, {"name": "WORKAREA_DESC", "description": "Language-Dependent Description of Work Area/Pattern", "isGid": false, "length": "60", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACC_LOC", "description": "Accident Location", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "ACC_LOC_DESC", "description": "Accident Location", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}]}}, "BODY_PARTS": {"description": "body parts", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "BODY_PARTS_HEADER": {"className": "com.plastipak.ehs.be.BODY_PARTS_HEADER", "header": true, "field": [{"name": "BODYPART_CODE", "description": "Phrase Library with Phrase Key", "isGid": true, "length": "21", "mandatory": true, "sqlType": "TEXT"}, {"name": "BODYPART_DESC", "description": "Phrase Text", "isGid": false, "length": "132", "mandatory": false, "sqlType": "TEXT"}]}}, "CAUSE_DESC": {"description": "Cuase desc", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CAUSE_DESC_HEADER": {"className": "com.plastipak.ehs.be.CAUSE_DESC_HEADER", "header": true, "field": [{"name": "CAUSE", "description": "Safety Measure Type", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "INCI_TYPE", "description": "Incident/Accident Log Entry Type", "isGid": true, "length": "3", "mandatory": true, "sqlType": "TEXT"}, {"name": "CAUSE_DESC", "description": "Description of Safety Measure Type", "isGid": false, "length": "120", "mandatory": false, "sqlType": "TEXT"}]}}, "CONVERSATION": {"description": "Incident structure", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "CONVERSATION_HEADER": {"className": "com.plastipak.ehs.be.CONVERSATION_HEADER", "header": true, "field": [{"name": "INCIDENT_NO", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_TYPE", "description": "Incident/Accident Log Entry Type", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_DATE", "description": "Event Date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_TIME", "description": "Event Time", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMP_NO", "description": "Person Involved", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INITIATOR", "description": "Person Involved", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORKAREA", "description": "Work Area or Pattern", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "AC_LOC", "description": "Accident Location", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "FUNC_LOC", "description": "Functional Location", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONV_TOPIC", "description": "Conversation Topic", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "SIGN_ACTIVITY", "description": "Significant activity(header desc)", "isGid": false, "length": "60", "mandatory": false, "sqlType": "TEXT"}, {"name": "DETAIL_DESC", "description": "Detailed description", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONV_TYPE", "description": "Conversation Type", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "SYSID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CONVERSATION_ATTACHMENT": {"description": "Attachment", "className": "com.plastipak.ehs.be.CONVERSATION_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "CONVERSATION_TOPIC": {"description": "conversation topic", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CONVERSATION_TOPIC_HEADER": {"className": "com.plastipak.ehs.be.CONVERSATION_TOPIC_HEADER", "header": true, "field": [{"name": "CONV_TOPIC", "description": "Characteristic Value", "isGid": true, "length": "30", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONV_TOPIC_DESC", "description": "Characteristic value description", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}]}}, "CONVERSATION_TYPE": {"description": "conversation type", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CONVERSATION_TYPE_HEADER": {"className": "com.plastipak.ehs.be.CONVERSATION_TYPE_HEADER", "header": true, "field": [{"name": "CONV_TYPE", "description": "Characteristic Value", "isGid": true, "length": "30", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONV_TYPE_DESC", "description": "Characteristic value description", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}]}}, "DUMMY_BE": {"description": "Description", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "DUMMY_BE_HEADER": {"className": "com.plastipak.ehs.be.DUMMY_BE_HEADER", "header": true, "field": [{"name": "INCIDENT_NO", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}]}, "DUMMY_BE_ATTACHMENT": {"description": "Attachment", "className": "com.plastipak.ehs.be.DUMMY_BE_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "EMPLOYEE": {"description": "Employee details", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "EMPLOYEE_HEADER": {"className": "com.plastipak.ehs.be.EMPLOYEE_HEADER", "header": true, "field": [{"name": "EMPLOYEE", "description": "Employee details", "isGid": true, "length": "8", "mandatory": true, "sqlType": "INTEGER"}, {"name": "FNAME", "description": "First Name", "isGid": false, "length": "25", "mandatory": false, "sqlType": "TEXT"}, {"name": "NACHN", "description": "Last Name", "isGid": false, "length": "25", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS", "description": "Employment Status", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "SYSID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "FUNC_LOC": {"description": "Functional location", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FUNC_LOC_HEADER": {"className": "com.plastipak.ehs.be.FUNC_LOC_HEADER", "header": true, "field": [{"name": "WORKAREA", "description": "Work Area or Pattern", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "FUNC_LOC", "description": "Functional Location", "isGid": true, "length": "30", "mandatory": true, "sqlType": "TEXT"}, {"name": "FUNC_LOC_DESC", "description": "Description of functional location", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}]}}, "IMMI_RESPONSE": {"description": "Immidiate response table", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "IMMI_RESPONSE_HEADER": {"className": "com.plastipak.ehs.be.IMMI_RESPONSE_HEADER", "header": true, "field": [{"name": "IMMI_RES_CODE", "description": "Characteristic Value", "isGid": true, "length": "30", "mandatory": true, "sqlType": "TEXT"}, {"name": "IMMI_RES_DESC", "description": "Characteristic value description", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}]}}, "INCIDENT": {"description": "Incident structure", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "INCIDENT_HEADER": {"className": "com.plastipak.ehs.be.INCIDENT_HEADER", "header": true, "field": [{"name": "INCIDENT_NO", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "INCIDENT_TYPE", "description": "Incident/Accident Log Entry Type", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_DATE", "description": "Event Date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_TIME", "description": "Event Time", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMP_NO", "description": "Person Involved", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORKAREA", "description": "Work Area or Pattern", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "AC_LOC", "description": "Accident Location", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "FUNC_LOC", "description": "Functional Location", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "BODYPART", "description": "Body Part", "isGid": false, "length": "21", "mandatory": false, "sqlType": "TEXT"}, {"name": "INJURY", "description": "Injury or Illness", "isGid": false, "length": "21", "mandatory": false, "sqlType": "TEXT"}, {"name": "TYPE_OF_FIRSTAID", "description": "Event Description", "isGid": false, "length": "60", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIFT_INFO", "description": "Value with template", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "AC_CATEGORY", "description": "Accident Category", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "DETAIL_DESC", "description": "Detailed description", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IMMI_RES_CODE", "description": "Characteristic Value", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAUSE", "description": "Safety Measure Type", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAUSE_DESC", "description": "Description of Safety Measure Type", "isGid": false, "length": "120", "mandatory": false, "sqlType": "TEXT"}, {"name": "CLOSE_OUT", "description": "Close out", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "RISK_ASSESMENT", "description": "Is risk assesment required", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SYSID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SIGN_ACTIVITY", "description": "Significant activity(header desc)", "isGid": false, "length": "60", "mandatory": false, "sqlType": "TEXT"}]}, "INCIDENT_ATTACHMENT": {"description": "Attachment", "className": "com.plastipak.ehs.be.INCIDENT_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "INCIDENT_TYPES": {"description": "incident types", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INCIDENT_TYPES_HEADER": {"className": "com.plastipak.ehs.be.INCIDENT_TYPES_HEADER", "header": true, "field": [{"name": "INCI_TYPE", "description": "Incident/Accident Log Entry Type", "isGid": true, "length": "3", "mandatory": true, "sqlType": "TEXT"}, {"name": "INCI_TYPE_DESC", "description": "Description of Log Entry Type", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "HIDE_BODY_INJURY", "description": "Hide body and injury parts", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "INJURIES": {"description": "Injuries", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INJURIES_HEADER": {"className": "com.plastipak.ehs.be.INJURIES_HEADER", "header": true, "field": [{"name": "INJURY_CODE", "description": "Phrase Library with Phrase Key", "isGid": true, "length": "21", "mandatory": true, "sqlType": "TEXT"}, {"name": "INJURY_DESC", "description": "Phrase Text", "isGid": false, "length": "132", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_INCIDENT_ATTACHMENTS": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_INCIDENT_ATTACHMENTS_HEADER": {"className": "com.plastipak.ehs.be.INPUT_INCIDENT_ATTACHMENTS_HEADER", "header": true, "field": [{"name": "INCIDENT_NO", "description": "Account Number of <PERSON><PERSON>or or Creditor", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "SYSID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_PLANT": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_PLANT_HEADER": {"className": "com.plastipak.ehs.be.INPUT_PLANT_HEADER", "header": true, "field": [{"name": "PLANT", "description": "Plant", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "LANG", "description": "ABAP System Field: Language Key of Text Environment", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATE", "description": "Date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "SYSID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_SYSID": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_SYSID_HEADER": {"className": "com.plastipak.ehs.be.INPUT_SYSID_HEADER", "header": true, "field": [{"name": "SYSID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}]}}, "INSTANCE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INSTANCE_HEADER": {"description": "Description", "className": "com.plastipak.ehs.be.INSTANCE_HEADER", "header": true, "field": [{"name": "SYSID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PORT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "LANG": {"description": "all laguages", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LANG_HEADER": {"description": "all laguages", "className": "com.plastipak.ehs.be.LANG_HEADER", "header": true, "field": [{"name": "LANGU", "description": "Language Key", "isGid": true, "length": "1", "mandatory": true, "sqlType": "TEXT"}, {"name": "LANGU_TEXT", "description": "Name of Language", "isGid": false, "length": "16", "mandatory": false, "sqlType": "TEXT"}]}}, "MONITOR": {"description": "Incident log table", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "MONITOR_HEADER": {"className": "com.plastipak.ehs.be.MONITOR_HEADER", "header": true, "field": [{"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "INCIDENT_NO", "description": "Incident/Accident Log Entry", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "INCIDENT_TYPE", "description": "Incident/Accident Log Entry Type", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_DATE", "description": "Event Date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_TIME", "description": "Event Time", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMP_NO", "description": "Person Involved", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMP_NAME", "description": "Full Name of Person", "isGid": false, "length": "80", "mandatory": false, "sqlType": "TEXT"}, {"name": "REPORTED_BY", "description": "Full Name of Person", "isGid": false, "length": "80", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORKAREA", "description": "Work Area or Pattern", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORKAREA_DESC", "description": "Language-Dependent Description of Work Area/Pattern", "isGid": false, "length": "60", "mandatory": false, "sqlType": "TEXT"}, {"name": "AC_LOC", "description": "Accident Location", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "AC_LOC_DESC", "description": "Description of Accident Location", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "FUNC_LOC", "description": "Functional Location", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "BODYPART", "description": "Body Part", "isGid": false, "length": "21", "mandatory": false, "sqlType": "TEXT"}, {"name": "INJURY", "description": "Injury or Illness", "isGid": false, "length": "21", "mandatory": false, "sqlType": "TEXT"}, {"name": "DETAIL_DESC", "description": "Detailed description", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAUSE", "description": "Safety Measure Type", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAUSE_DESC", "description": "Description of Safety Measure Type", "isGid": false, "length": "120", "mandatory": false, "sqlType": "TEXT"}, {"name": "CLOSE_OUT", "description": "Close out", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXECUTION_DATE", "description": "Execution Date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}]}, "ATTACHMENTS": {"className": "com.plastipak.ehs.be.ATTACHMENTS", "field": [{"name": "NAME", "description": "name of the attachment", "isGid": true, "length": "40", "mandatory": true, "sqlType": "TEXT"}, {"name": "FILE_TYPE", "description": "Document type PDF, DOC, TXT, IMG", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Picture type PDF, TEXT, PNG, JPEG", "isGid": false, "length": "5", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATA", "description": "BASE64 of Bytes", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_NO", "description": "Incident/Accident Log Entry", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}]}}, "OBSERVATION": {"description": "Incident structure", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "OBSERVATION_HEADER": {"description": "Incident structure", "className": "com.plastipak.ehs.be.OBSERVATION_HEADER", "header": true, "field": [{"name": "INCIDENT_NO", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_TYPE", "description": "Incident/Accident Log Entry Type", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_DATE", "description": "Event Date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "INCIDENT_TIME", "description": "Event Time", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMP_NO", "description": "Person Involved", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "OBSERVER", "description": "Person Involved", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORKAREA", "description": "Work Area or Pattern", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "AC_LOC", "description": "Accident Location", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "FUNC_LOC", "description": "Functional Location", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "OB_TASK", "description": "Conversation Topic", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "SIGN_ACTIVITY", "description": "Significant activity(header desc)", "isGid": false, "length": "60", "mandatory": false, "sqlType": "TEXT"}, {"name": "DETAIL_DESC", "description": "Detailed description", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAUSE", "description": "Safety Measure Type", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAUSE_DESC", "description": "Description of Safety Measure Type", "isGid": false, "length": "120", "mandatory": false, "sqlType": "TEXT"}, {"name": "CLOSE_OUT", "description": "Close out", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SYSID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CUES": {"className": "com.plastipak.ehs.be.CUES", "field": [{"name": "CUE_NAME", "description": "Cue name", "isGid": true, "length": "30", "mandatory": true, "sqlType": "TEXT"}, {"name": "CUE_DESC", "description": "cue desc", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "CUE_VALUE", "description": "Safe/Unsafe/N/A( S/U/N)", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "OBSERVATION_ATTACHMENT": {"description": "Attachment", "className": "com.plastipak.ehs.be.OBSERVATION_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "OBSERV_CUES": {"description": "Observaton cues", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "OBSERV_CUES_HEADER": {"className": "com.plastipak.ehs.be.OBSERV_CUES_HEADER", "header": true, "field": [{"name": "CUE_NAME", "description": "Cue name", "isGid": true, "length": "30", "mandatory": true, "sqlType": "TEXT"}, {"name": "CUE_DESC", "description": "cue desc", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "CUE_VALUE", "description": "S \\ U \\ N", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "OBSERV_TASKS": {"description": "ehs observation task", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "OBSERV_TASKS_HEADER": {"className": "com.plastipak.ehs.be.OBSERV_TASKS_HEADER", "header": true, "field": [{"name": "OB_TASK", "description": "Characteristic Value", "isGid": true, "length": "30", "mandatory": true, "sqlType": "TEXT"}, {"name": "OB_TASK_DESC", "description": "Characteristic value description", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}]}}, "PLANT": {"description": "All plants", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PLANT_HEADER": {"className": "com.plastipak.ehs.be.PLANT_HEADER", "header": true, "field": [{"name": "PLANT", "description": "Plant", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT_NAME", "description": "Name", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}]}}, "USER_DETAILS": {"description": "EHS login user details", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "USER_DETAILS_HEADER": {"className": "com.plastipak.ehs.be.USER_DETAILS_HEADER", "header": true, "field": [{"name": "USER_ID", "description": "User name", "isGid": true, "length": "12", "mandatory": true, "sqlType": "TEXT"}, {"name": "FULLNAME", "description": "Full Name of Person", "isGid": false, "length": "80", "mandatory": false, "sqlType": "TEXT"}, {"name": "E_MAIL", "description": "E-Mail Address", "isGid": false, "length": "241", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "LANG", "description": "Users Language", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMP_NO", "description": "Person Involved", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}]}}, "WORKAREAS": {"description": "work area", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "WORKAREAS_HEADER": {"className": "com.plastipak.ehs.be.WORKAREAS_HEADER", "header": true, "field": [{"name": "PLANT", "description": "Plant", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "WORKAREA", "description": "Work Area or Pattern", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "RECNROOT", "description": "Sequential Number of Data Record", "isGid": true, "length": "20", "mandatory": true, "sqlType": "INTEGER"}, {"name": "WORKAREA_DESC", "description": "Language-Dependent Description of Work Area/Pattern", "isGid": false, "length": "60", "mandatory": false, "sqlType": "TEXT"}]}}, "Index": []}