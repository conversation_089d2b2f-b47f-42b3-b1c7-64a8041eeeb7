import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full'
  }, 
  {
    path: 'login',
    loadChildren: () => import('./login/login.module').then( m => m.LoginPageModule),
  },
  {
    path: 'home',
    loadChildren: () => import('./home/<USER>').then(m => m.HomePageModule),
  },
  {
    path: 'safety-observation',
    loadChildren: () => import('./safety-observation/safety-observation.module').then( m => m.SafetyObservationPageModule)
  },
  {
    path: 'safety-incident',
    loadChildren: () => import('./safety-incident/safety-incident.module').then( m => m.SafetyIncidentPageModule)
  },
  {
    path: 'safety-conversation',
    loadChildren: () => import('./safety-conversation/safety-conversation.module').then( m => m.SafetyConversationPageModule)
  },
  {
    path: 'property-damage',
    loadChildren: () => import('./property-damage/property-damage.module').then( m => m.PropertyDamagePageModule)
  },
  {
    path: 'environment-incident',
    loadChildren: () => import('./environment-incident/environment-incident.module').then( m => m.EnvironmentIncidentPageModule)
  },
  {
    path: 'incident-log-monitor',
    loadChildren: () => import('./incident-log-monitor/incident-log-monitor.module').then( m => m.IncidentLogMonitorPageModule)
  },
  {
    path: 'log-monitor-attachments',
    loadChildren: () => import('./log-monitor-attachments/log-monitor-attachments.module').then( m => m.LogMonitorAttachmentsPageModule)
  },

];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
