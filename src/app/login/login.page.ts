import { Component, NgZone, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';


import {
  AuthenticateActivateResult,
  AuthenticateAndActivateResultType,
  LoginListenerType,
  LoginParameters,
  LoginType,
  ResultType,
  SyncResult,
  UnviredCordovaSDK,
  UnviredCredential,
  UnviredResult,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from 'src/app/services/data.service';
import { AppConstants } from '../Constants/app-constants';
import { TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {
  public passwordType: string;
  public fillDetails: boolean;
  public errorMessage: string;
  public userName: string;
  public password: string;
  public url: string = AppConstants.UMP_URL;
  public loginResultType: LoginListenerType = null;
  public loginDetails: any = {};
  public SYSID: string;
  public SAP_PORT: string;
  public instance: any;
  public selInstance: "";
  public instanceData: any = [
    { SYSID: "TST", }, { SYSID: "QAE", }];
  public hosturl: any;
  public checkInstance: boolean;
  responseToken: any;

  constructor(
    public ngZone: NgZone,
    public router: Router,
    private route: ActivatedRoute,
    public unviredSdk: UnviredCordovaSDK,
    public dataService: DataService,
    private translate: TranslateService,
    public appConstants: AppConstants,
    private http: HttpClient,

  ) {
    this.passwordType = 'password';
    this.fillDetails = false;
    this.errorMessage = '';
    this.userName = '';
    this.password = '';
  }

  async ngOnInit() {
    this.translate.use("en");
    let inputdataOld = JSON.parse(localStorage.getItem('querydata'));
    if (inputdataOld == null) {
      this.selInstance = "";
    } else {
      this.selInstance = inputdataOld.SYSID;
      this.setPort(this.selInstance)
    }
    console.log("selInstance", this.selInstance);

    this.unviredSdk.logDebug('LoginPage', 'login', 'Processing user login');
    await this.pleaseWaitLoader();
    try {
      this.errorMessage = '';
      if (!this.dataService.isNetworkConnected) {
        this.unviredSdk.logError(
          'LoginPage',
          'login',
          'Please check internet connection and try again!'
        );
        this.errorMessage = this.translate.instant('Please check internet connection and try again!');
        return;
      }

      // await this.dataService.clearData();
      this.errorMessage = '';

      this.hosturl = this.dataService.getHostURL();
      if (this.hosturl.toLocaleLowerCase().includes("plastipak.com")) {
        this.checkInstance = true;
        let inputDataNew = { SYSID: 'PRD' };
        let port = 'EHS_SAP_ECC_PORT_1'
        localStorage.setItem('querydata', JSON.stringify(inputDataNew));
        localStorage.setItem('portData', JSON.stringify(port));
      }
      else if (this.hosturl.toLocaleLowerCase().includes("plastipak.eu")) {
        this.checkInstance = true;
        let inputDataNew = { SYSID: 'PRE' };
        let port = 'EHS_SAP_ECC_PORT'
        localStorage.setItem('querydata', JSON.stringify(inputDataNew));
        localStorage.setItem('portData', JSON.stringify(port));
      }
      else {
        this.checkInstance = false;
        let inputDataNew = { SYSID: this.selInstance };
        localStorage.setItem('querydata', JSON.stringify(inputDataNew));
      }
      this.saveDataToLocalStorage();
    }
    finally {
      // Ensure loader is dismissed in all cases
       await this.dismissLoadingController();
    }
  }

  async ionViewWillEnter() {
    this.userName = '';
    this.password = '';
    this.errorMessage = '';
  }

  loginKeyEvent(e: any) {
    if (e.key == 'Enter') {
      this.login();
    }
  }

  // User Login
  async login() {
    let inputdataOld = JSON.parse(localStorage.getItem('querydata'));
    if (inputdataOld == null) {
      this.selInstance = "";
    } else {
      this.selInstance = inputdataOld.SYSID;
      this.setPort(this.selInstance)
    }
    console.log("selInstance", this.selInstance);
    let inputDataNew = { SYSID: this.selInstance };
    localStorage.setItem('querydata', JSON.stringify(inputDataNew));

    if (this.selInstance != "") {
      function convertToBase64(data: any): string { return btoa(data); }
      let portName = JSON.parse(localStorage.getItem('portData'));
      if(portName == null || portName === "") {
        if (this.hosturl.toLocaleLowerCase().includes("plastipak.com")) {
          portName = 'EHS_SAP_ECC_PORT_1';
          localStorage.setItem('portData', JSON.stringify(portName));
        }
        else if (this.hosturl.toLocaleLowerCase().includes("plastipak.eu")) {
          portName = 'EHS_SAP_ECC_PORT';
          localStorage.setItem('portData', JSON.stringify(portName));
        }
        else {
          this.errorMessage = this.translate.instant('Please reload the page and try again!');
          return;
        }      
      }
      let rawJsonString = { USERID: this.userName.trim(), PSWD: this.password.trim(), PORT : portName };
      const raw = convertToBase64(JSON.stringify(rawJsonString));

      try {
        await this.pleaseWaitLoader();
        console.log("AUTH login start");
        this.responseToken = await this.http.post<any>(this.dataService.getAUTHUrl(), raw, { responseType: 'json' }).toPromise();

        this.unviredSdk.logDebug('LoginPage', 'login', 'Processing user login');
        this.errorMessage = '';
        if (!this.dataService.isNetworkConnected) {
          this.unviredSdk.logError(
            'LoginPage',
            'login',
            'Please check internet connection and try again!'
          );
          this.errorMessage = this.translate.instant('Please check internet connection and try again!');
          return;
        }

        await this.dataService.clearData();
        this.errorMessage = '';

        let loginParameters = new LoginParameters();
        loginParameters.appName = 'EHS';
        loginParameters.url = this.dataService.getUMPUrl();
        loginParameters.company = 'PLASTIPAK';
        loginParameters.username = 'UNVIRED';
        loginParameters.loginType = LoginType.saml2;
        loginParameters.domain = 'PLASTIPAK';
        loginParameters.jwtOptions = { app: 'EHS' };
        loginParameters.metadataPath = 'assets/metadata.json';
        loginParameters['appVersion'] = this.appConstants.APP_RELEASE_NUMBER;
        loginParameters['samlToken'] = this.responseToken.token;

        try {
          this.unviredSdk.logDebug(
            'LoginPage',
            'login',
            'Calling Authenticate And Activate'
          );
          let authenticateActivateResult: AuthenticateActivateResult;
          try {
            console.log("unviredSdk.authenticateAndActivate() start");
            authenticateActivateResult =
              await this.unviredSdk.authenticateAndActivate(loginParameters);
            console.log("unviredSdk.authenticateAndActivate() end");
            if (
              authenticateActivateResult.type ===
              AuthenticateAndActivateResultType.auth_activation_success
            ) {
              this.unviredSdk.logDebug(
                'LoginPage',
                'login',
                'Authenticate And Activation Success'
              );

              // Saving login details into local storage.
              this.saveDataToLocalStorage();

              let credentials: UnviredCredential[] = [];
              // Check is credentials are already set or need to set.
              let isCredentialsRequriedResult: UnviredResult =
                await this.unviredSdk.isClientCredentialsSet();
              if (
                isCredentialsRequriedResult &&
                isCredentialsRequriedResult.data === false
              ) {
                localStorage.setItem("userName", this.userName);
                let inputdataInstance = JSON.parse(localStorage.getItem('querydata'));
                let portName = JSON.parse(localStorage.getItem('portData'));
                if (inputdataInstance && portName) {

                  this.SAP_PORT = portName
                } else {
                  // this.errorMessage = "Please provide Instance ID in the URL."
                  return
                }
                // console.log("getInstance", getInstance[0].PORT_NAME);
                credentials = [
                  {
                    user: this.userName.trim(),
                    password: this.password.trim(),
                    port: this.SAP_PORT,
                  },
                ];
                await this.unviredSdk.setClientCredentials(credentials);
                localStorage.setItem(
                  'clientDetails',
                  btoa(JSON.stringify(credentials))
                );
              }

              let inputdata = JSON.parse(localStorage.getItem('querydata'));
              if (inputdata) {
                // Calling get user profile
                let userProfileResult: any = await this.dataService.getProfile(inputdata);
                this.dataService.setProfileCallIsDone(true);
                this.dataService.setFirstTimeFlag(false);
                if (userProfileResult) {
                  if (userProfileResult.type == ResultType.success) {
                    if (
                      userProfileResult.data &&
                      userProfileResult.data.USER_DETAILS.length > 0 &&
                      userProfileResult.data.USER_DETAILS[0]
                        .USER_DETAILS_HEADER
                    ) {
                      // this.dataService.restartIdleTimer();
                      this.ngZone.run(() => this.router.navigate(['home']));
                    } else {
                      this.errorMessage =
                        this.translate.instant('Something went wrong while login, please try again!');
                    }
                  } else {
                    let errorMsg =
                      this.dataService.handleInfoMessage(userProfileResult);
                    if (errorMsg && errorMsg.length > 100) {
                      await this.dismissLoadingController();
                      await this.dataService.displayAlert(errorMsg);
                    } else {
                      this.errorMessage = errorMsg;
                    }
                  }
                } else {
                  this.errorMessage =
                    this.translate.instant('Something went wrong while login, please try again!');
                }
              } else {
                await this.dataService.logout();
                localStorage.setItem('querydata', JSON.stringify(inputdata));
                // this.errorMessage = "Please provide Instance ID in the URL."
              }
            } else if (
              authenticateActivateResult.type ===
              AuthenticateAndActivateResultType.auth_activation_error
            ) {
              this.unviredSdk.logError(
                'LoginPage',
                'login',
                'Auth Activation Error'
              );
              let authErrorMsg = authenticateActivateResult.error;
              if (authErrorMsg && authErrorMsg.length > 100) {
                await this.dismissLoadingController();
                await this.dataService.displayAlert(authErrorMsg);
              } else {
                this.errorMessage = authErrorMsg;
              }
            }
          } catch (error) {
            this.unviredSdk.logError(
              'LoginPage',
              'login',
              'Error in calling Authenticate And Activate'
            );
            this.errorMessage = error;
          }
        } catch (error) {
          this.unviredSdk.logError('LoginPage', 'login', 'Authentication failed!');
          this.errorMessage = error;
        }

      }
      catch (error) {
        console.error('Error occurred:', error);
        await this.dismissLoadingController();
        await this.dataService.displayAlert(error.error.error);
        this.errorMessage = error.error.error;
      }
      finally {
        // Ensure loader is dismissed in all cases
        await this.dismissLoadingController();
      }

    } else {
      await this.dismissLoadingController();
      await this.dataService.displayAlert("Select Instance");
    }
  }

  async selectedInstance(event: any) {
    let selectedValue = event.target.value;
    console.log("Selected Value of Instance", selectedValue);
    let inputDataNew = { SYSID: selectedValue };
    localStorage.setItem('querydata', JSON.stringify(inputDataNew));
    this.setPort(selectedValue)
  }
  setPort(portName: any) {
    if (portName == 'TST') {
      let port = 'EHS_SAP_ECC_PORT'
      localStorage.setItem('portData', JSON.stringify(port));
    } else if (portName == 'QAE') {
      let port = 'EHS_SAP_ECC_PORT_1'
      localStorage.setItem('portData', JSON.stringify(port));
    }
  }

  emailBlur(event: any) {
    this.errorMessage = '';
    this.fillDetails = false;
  }

  passwordBlur(event: any) {
    this.errorMessage = '';
    this.fillDetails = false;
  }

  // Display loader
  async pleaseWaitLoader() {
    await this.dataService.pleaseWaitLoader();
  }
  async dismissLoadingController() {
    await this.dataService.dismissLoadingController();
  }

  togglePasswordMode() {
    this.passwordType = this.passwordType === 'text' ? 'password' : 'text';
  }

  async saveDataToLocalStorage() {
    localStorage.setItem('url', this.dataService.getUMPUrl());
  }
}
