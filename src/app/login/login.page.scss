.login-background-container {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  width: 100%;
  height: 100vh;
  background-color: #d3d3d3;
}

.container {
  /* Must manually set width/height */
  width: 1000px;
  height: 570px;

  /* The magic centering code */
  margin: auto;
  position: absolute;
  top: 0;
  bottom: 0; /* Aligns Vertically - Remove for Horizontal Only */
  left: 0;
  right: 0; /* Aligns Horizontally - Remove for Vertical Only  */

  /* Prevent div from overflowing main window */
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;

  background: #fff;
  border-radius: 5px;
  -webkit-box-shadow: 4px 4px 20px -1px rgba(59, 59, 59, 0.6);
  -moz-box-shadow: 4px 4px 20px -1px rgba(59, 59, 59, 0.6);
  box-shadow: 4px 4px 20px -1px rgba(59, 59, 59, 0.6);
}

.container:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 66%;
  top: 0%;
  bottom: 0%;
  // background: url("/assets/img/LoginBG.png");
  background: url("/assets/img/LoginBG.png");
  transform: skewX(50deg) rotate(0deg);
}

.top-left {
  padding-top: 20px;
  padding-left: 35px;
  padding-bottom: 0px;
  background-color: #fff;
}

.top-right {
  position: absolute;
  top: 8px;
  right: 16px;
  color: black;
  text-align: right;
}

.login-msg {
  color: black;
  font-size: 24px;
}

.input-field-width {
  margin-top: 15px;
  width: 45%;
}

.header {
  font-size: 28px;
  font-weight: 500;
  color: #fff;
  margin: 15px 20px 0px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-left: 25%;
}

.login-form-container {
  // margin-left: 40px;
  margin-top: 55px;
  padding: 20px;
}

.login-text {
  text-decoration: none;
  color: #1c8ac9;
  cursor: pointer;
}

.first-time-login {
  margin-top: 10px;
  margin-left: 0px;
  max-width: 45%;
}

.forget-password {
  padding-right: 0px;
  float: right;
}

.button-container {
  max-width: 45%;
  margin-top: 20px;
}

.login-button {
  margin-top: 15px;
  float: right;
  text-align: center;
  margin: 0;
  min-width: 64px;
  font-weight: 500;
  font-size: 16px;
  font-family: Roboto, BlinkMacSystemFont, "Helvetica Neue", Arial,
    "Microsoft Yahei", "WenQuanYi Micro Hei", "Pingfang SC", sans-serif;
}

.password-field {
  color: #404040;
  float: right;
  margin-top: -27px;
  margin-right: 8px;
  position: relative;
  z-index: 2;
  cursor: pointer;
}

.input-style {
  border: 1px solid #999999;
  height: 30px;
}

::placeholder {
  color: rgb(192, 192, 192);
  opacity: 1;
}

:-ms-input-placeholder {
  color: rgb(192, 192, 192);
}

::-ms-input-placeholder {
  color: rgb(192, 192, 192);
}

@media screen and (min-width: 601px) and (max-width: 800px) {
  .container::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 550px;
    top: 0%;
    bottom: 0%;
  }
}

@media screen and (min-width: 301px) and (max-width: 600px) {
  .header {
    font-weight: 500;
    color: black;
    font-size: 20px;
    margin-top: 5px;
  }
}
@media screen and (min-width: 100px) and (max-width: 300px) {
  .top-right {
    position: absolute;
    color: rgba(255, 255, 255, 0);
    text-align: center;
    width: 100%;
    right: 0 !important;
  }

  .top-left {
    display: flex;
    justify-content: center;
    padding-top: 40px;
    padding-left: 0;
    padding-bottom: 0px;
    background-color: #fff;
  }
  .header {
    font-weight: 500;
    color: black;
    font-size: 20px;
    margin-top: 5px;
  }
}

@media screen and (min-width: 301px) and (max-width: 600px) {
  .container::before {
    width: 100%;
    content: none;
    margin: 0 auto;
  }

  .container {
    width: 90%;
    content: none;
    margin: 0 auto;
    margin-top: 10%;
    padding-left: 0px;
  }

  .login-form-container {
    margin-top: 15px;
    text-align: center;
    padding-top: 50px;
  }

  .input-field-width {
    margin-top: 15px;
    width: 100%;
  }

  .top-right {
    position: absolute;
    color: rgba(255, 255, 255, 0);
    text-align: center;
    width: 100%;
    right: 0 !important;
  }

  .top-left {
    display: flex;
    justify-content: center;
    padding-top: 40px;
    padding-left: 0;
    padding-bottom: 0px;
    background-color: #fff;
  }

  .button-container {
    max-width: 100%;
  }

  .use-different-account {
    margin-top: 10px;
    margin-left: 0px;
    max-width: 100%;
  }

  .local-email-user {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 20px;
    color: grey;
    text-decoration: none;
    max-width: 100%;
    box-sizing: border-box;
    border-bottom: 0.5px grey;
  }
}

@media screen and (max-width: 300px) {
  .container::before {
    width: 50%;
    content: none;
    margin: 0 auto;
  }

  .login-form-container {
    // margin-left: 30px;
    margin-top: 40px;
    padding: 20px;
  }
}

.container-div {
  overflow-y: auto;
  padding-right: 0px !important;
}
.form-control{
  // width: 100%;
  //   height: 40px;
  //   border-radius: 5px;
    // text-align: center;
      display: block;
      width: 100%;
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.5;
      color: #212529;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      border-radius: 0.25rem;
      transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
