<ion-content class="ion-padding">
  <div class="login-background-container">
    <div class="container container-div">

      <div class="top-right">
        <div class="header">{{"Environment Health & Safety Application" | translate}}</div>
      </div>

      <div class="login-form-container">
        <img src="assets/img/plastipak-logo.png" style="width: 200px;">

        <form #loginForm="ngForm">
          <div class="login-msg">SAP Login</div>
          <div class="form-group input-field-width">
            <input spellcheck="false" autocomplete="off" type="text" class="form-control" #userId="ngModel"
              [(ngModel)]="userName" name="userId" placeholder="{{ 'SAP User Id' | translate }}"
              [class.is-dirty]="userId.dirty && userId.touched" (input)="emailBlur($event)" required maxlength="241" />
            <div *ngIf="userId.errors && (userId.dirty || userId.touched)" style="margin-top: 2px">
              <small *ngIf="userId.errors.required" class="text-danger">{{"SAP User Id is required." |
                translate}}</small>
            </div>
          </div>
          <div class="form-group input-field-width">
            <input [type]="passwordType" class="form-control" #pwd="ngModel" [(ngModel)]="password" name="password"
              placeholder="{{ 'Password' | translate }}" [class.is-dirty]="pwd.dirty && pwd.touched"
              (input)="passwordBlur($event)" required maxlength="30" (keyup)="loginKeyEvent($event)" />
            <span (click)="togglePasswordMode()" class="password-field" *ngIf="passwordType === 'password'">
              <ion-icon name="eye-off-outline"></ion-icon>
            </span>
            <span (click)="togglePasswordMode()" class="password-field" *ngIf="passwordType == 'text'">
              <ion-icon name="eye-outline"></ion-icon>
            </span>
            <div *ngIf="pwd.errors && (pwd.dirty || pwd.touched)" style="margin-top: 2px">
              <small *ngIf="pwd.errors.required" class="text-danger">{{"Password is required." | translate}}</small>
            </div>

            <div *ngIf="checkInstance != true">
              <ion-row style="margin-left: -5px;">
                <ion-col size="4" style="text-align: left;">
                  <!-- <ion-label>{{"Instance" | translate}}</ion-label> -->
                  <select style="border-radius: 5px; height: 35px;width:100%; margin-top: 5px;"
                    [(ngModel)]="selInstance" value="selInstance" (change)="selectedInstance($event)"
                    #selInstanceNgModel="ngModel" name="selInstanceNgModel" required>
                    <option disabled value="" selected>{{"Select Instance" | translate}}</option>
                    <option *ngFor="let item of instanceData" value="{{item.SYSID}}">
                      {{item.SYSID}}</option>
                  </select>
                </ion-col>
              </ion-row>
            </div>

          </div>
          <div class="button-container">
            <ion-button mode="md" type="submit" color="lightBlue"
              [disabled]="(pwd.errors || pwd.invalid) || (userId.errors || userId.invalid)" strong
              routerDirection="forward" class="login-button" (click)="login()">&nbsp; &nbsp; {{"Login" | translate}}
              &nbsp; &nbsp;
            </ion-button>
          </div>
        </form>
      </div>

      <div style="margin-left: 30px !important; margin-top: -12px">
        <small class="text-danger" style="font-size: 17px">{{errorMessage}}</small>
      </div>
    </div>
  </div>
</ion-content>