export class AppConstants {
    // Constants
    public static ATTACHMENT_URL = 'API/v2/applications/';
    public static APPLICATION_NAME = 'EHS';

    public APP_RELEASE_NUMBER = '99.99.99';
    public APP_RELEASE_DATE = '@@RELEASE_DATE@@';
    public APP_NAME = 'EHS';
    public static UMP_URL = 'https://absomi01.absopure.com:8443/UMP/';
    public static SAP_PORT = '';

    //PA functions
    public static PA_GET_MASTERDATA = 'EHS_PA_GET_MASTERDATA';
    public static PA_GET_PROFILE = 'EHS_PA_GET_PROFILE';
    public static PA_LOOKUP_EMPLOYEE = 'EHS_PA_LOOKUP_EMPLOYEE';
    public static PA_CREATE_INCIDENT = 'EHS_PA_CREATE_INCIDENT'
    public static PA_CREATE_CONVERSATION = 'EHS_PA_CREATE_CONVERSATION'
    public static PA_CREATE_OBSERVATION = 'EHS_PA_CREATE_OBSERVATION'
    public static PA_GET_INCIDENTS = 'EHS_PA_GET_INCIDENTS'
    public static PA_UPDATE_INCIDENT = 'EHS_PA_UPDATE_INCIDENT'
    public static PA_GET_INSTANCE_PORT_NAMES = 'EHS_PA_GET_INSTANCE_PORT_NAMES'
    public static PA_GET_INCIDENT_ATTACHMENTS = 'EHS_PA_GET_INCIDENT_ATTACHMENTS'


}

export enum EhsStatus {
    OPEN = 'OPEN',
    IN_PROCESS = 'IN_PROCESS',
    INSPECTED = 'INSPECTED',
    IN_REVIEW = 'IN_REVIEW',
    FOR_REPAIR = 'FOR_REPAIR',
    IN_REPAIR = 'IN_REPAIR',
    FOR_VERIFY = 'FOR_VERIFY',
    COMPLETED = 'COMPLETED',
    CANCELLED = 'CANCELLED',
    RECOMMENDREPAIR = 'RECOMMENDREPAIR',
    SUBMITFORVERIFICATION = 'SUBMITFORVERIFICATION',
    SENDBACKFORREVIEW = 'SENDBACKFORREVIEW',
    CLOSEINSPECTION = 'CLOSEINSPECTION',
    REVIEWED = 'REVIEWED',
}
