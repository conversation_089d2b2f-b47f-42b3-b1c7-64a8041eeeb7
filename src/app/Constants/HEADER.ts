export class DATA_STRUCTURE {
    LID: string;
    FID: string;
    OBJECT_STATUS: number = 1;
    SYNC_STATUS: number = 0;
}

export class ACC_LOCS_HEADER extends DATA_STRUCTURE {
    PLANT: string;
    WORKAREA: string;
    RECNROOT: number;
    WORKAREA_DESC: string;
    ACC_LOC: string;
    ACC_LOC_DESC: string
}
export class BODY_PARTS_HEADER extends DATA_STRUCTURE {
    BODYPART_CODE: string;
    BODYPART_DESC: string;
}
export class EMPLOYEE_HEADER extends DATA_STRUCTURE {
    EMPLOYEE: number;
    FNAME: string;
    NACHN: string;
    STATUS: string;
    SYSID: string;
}
export class FUNC_LOC_HEADER extends DATA_STRUCTURE {
    WORKAREA: string;
    FUNC_LOC: string;
    FUNC_LOC_DESC: string
}

export class INCIDENT_HEADER extends DATA_STRUCTURE {
    INCIDENT_NO: string;
    PLANT: string;
    INCIDENT_TYPE: string;
    INCIDENT_DATE: string;
    INCIDENT_TIME: string;
    EMP_NO: string;
    WORKAREA: string;
    AC_LOC: string;
    FUNC_LOC: string;
    BODYPART: string;
    INJURY: string;
    TYPE_OF_FIRSTAID: string;
    SHIFT_INFO: string;
    AC_CATEGORY: string;
    DETAIL_DESC: string;
    CAUSE: string;
    CAUSE_DESC: string;
    CLOSE_OUT = "unchecked";
    RISK_ASSESMENT: string;
    SYSID: string;
    SIGN_ACTIVITY: string;
}

export class OBSERVATION_HEADER extends DATA_STRUCTURE {
    INCIDENT_NO: string;
    PLANT: string;
    INCIDENT_TYPE: string;
    INCIDENT_DATE: string;
    INCIDENT_TIME: string;
    EMP_NO: string;
    OBSERVER: string;
    WORKAREA: string;
    AC_LOC: string;
    FUNC_LOC: string;
    OB_TASK: string;
    SIGN_ACTIVITY: string;
    DETAIL_DESC: string;
    CAUSE: string;
    CAUSE_DESC: string;
    CLOSE_OUT: "";
}
export class ATTACHMENT extends DATA_STRUCTURE {
    NAME: string;
    FILE_TYPE: string;
    MIME_TYPE: string;
    DATA: string;
}

export class INCIDENT_TYPES_HEADER extends DATA_STRUCTURE {
    INCI_TYPE: string;
    INCI_TYPE_DESC: string;
    HIDE_BODY_INJURY: string;
}

export class INJURIES_HEADER extends DATA_STRUCTURE {
    INJURY_CODE: string;
    INJURY_DESC: string;
}

export class INPUT_PLANT_HEADER extends DATA_STRUCTURE {
    PLANT: string;
}
export class OBSERV_CUES_HEADER extends DATA_STRUCTURE {
    CUE_NAME: string;
    CUE_DESC: string;
    SAFE: string;
    UNSAFE: string;
    NA: string;

}
export class USER_DETAILS_HEADER extends DATA_STRUCTURE {
    USER_ID: string;
    FULLNAME: string;
    E_MAIL: string;
    PLANT: string;
    EMP_NO: string;
}

export class WORKAREAS_HEADER extends DATA_STRUCTURE {
    PLANT: string;
    WORKAREA: string;
    RECNROOT: number;
    WORKAREA_DESC: string;
}

export class IMMI_RESPONSE_HEADER extends DATA_STRUCTURE {
    IMMI_RES_CODE: string;
    IMMI_RES_DESC: string;
}

export class CAUSE_DESC_HEADER extends DATA_STRUCTURE {
    CAUSE: string;
    CAUSE_DESC: string;
}

export class INSTANCE_HEADER extends DATA_STRUCTURE {
    SYSID: string;
    PORT_NAME: string;
}

export class MONITOR_HEADER extends DATA_STRUCTURE {
    PLANT: string;
    INCIDENT_NO: string;
    INCIDENT_TYPE: string;
    INCIDENT_DATE: string;
    INCIDENT_TIME: string;
    EMP_NO: string;
    EMP_NAME: string;
    REPORTED_BY: string;
    WORKAREA: string;
    WORKAREA_DESC: string;
    AC_LOC: string;
    AC_LOC_DESC: string;
    FUNC_LOC: string;
    BODYPART: string;
    INJURY: string;
    DETAIL_DESC: string;
    CAUSE: string;
    CAUSE_DESC: string;
    CLOSE_OUT: string;
    EXECUTION_DATE: string;
}







