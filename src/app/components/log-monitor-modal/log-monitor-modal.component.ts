import { Component, OnInit, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController, AlertController } from '@ionic/angular';
import { INCIDENT_HEADER } from 'src/app/Constants/HEADER';
import { DataService } from 'src/app/services/data.service';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'app-log-monitor-modal',
  templateUrl: './log-monitor-modal.component.html',
  styleUrls: ['./log-monitor-modal.component.scss'],
})
export class LogMonitorModalComponent implements OnInit {
  public isMobileView: boolean = false;
  public causeFields = new INCIDENT_HEADER();
  public selectedCause = "";
  public envCause = [];
  public othCause = [];
  selectedIncidentData: any;
  public errorMessage: string = "";
  public INPUT_SYSID: any;
  public SYSID = "";

  constructor(
    public modalController: ModalController,
    public dataService: DataService,
    public alertController: AlertController,
    private translate: TranslateService,
    private ngZone: NgZone,
    public router: Router,

  ) { }

  ngOnInit() {
    this.INPUT_SYSID = JSON.parse(localStorage.getItem('querydata'));
    console.log("INPUT_SYSID", this.INPUT_SYSID.SYSID);
    this.loadData();
    console.log("selectedIncidentData.INCIDENT_TYPE", this.selectedIncidentData.INCIDENT_TYPE);
    console.log("Selected data",this.selectedIncidentData);
  }

  async loadData() {
    let causesENV = await this.dataService.getData('CAUSE_DESC_HEADER', " WHERE INCI_TYPE = 'ENV' ORDER BY CAUSE_DESC");
    if (causesENV.length > 0) {
      this.envCause = causesENV;
      console.log("envCause", this.envCause);
    } else {
      console.log("no cause list found");
    }

    let causesOTH = await this.dataService.getData('CAUSE_DESC_HEADER', " WHERE INCI_TYPE = 'OTH' ORDER BY CAUSE_DESC");
    if (causesOTH.length > 0) {
      this.othCause = causesOTH;
      console.log("othCause", this.othCause);
    } else {
      console.log("no cause list found");
    }
  }

  resizeScreen() {
    if (window.innerWidth < 650) {
      this.isMobileView = true;
    } else {
      this.isMobileView = false;
    }
  }

  async save() {
    this.SYSID = this.INPUT_SYSID.SYSID;
    console.log("SYSID", this.SYSID);
    try {
      await this.pleaseWaitLoader();
      let data = {
        INCIDENT: [
          {
            INCIDENT_HEADER: {
              INCIDENT_NO: this.selectedIncidentData.INCIDENT_NO,
              PLANT: this.selectedIncidentData.PLANT,
              INCIDENT_TYPE: this.selectedIncidentData.INCIDENT_TYPE,
              INCIDENT_DATE: this.selectedIncidentData.INCIDENT_DATE,
              INCIDENT_TIME: this.selectedIncidentData.INCIDENT_TIME,
              EMP_NO: this.selectedIncidentData.EMP_NO,
              WORKAREA: this.selectedIncidentData.WORKAREA,
              AC_LOC: this.selectedIncidentData.AC_LOC,
              FUNC_LOC: this.selectedIncidentData.FUNC_LOC,
              BODYPART: this.selectedIncidentData.BODYPART,
              INJURY: this.selectedIncidentData.INJURY,
              CLOSE_OUT: this.causeFields.CLOSE_OUT,
              CAUSE: this.selectedCause,
              CAUSE_DESC: this.causeFields.CAUSE_DESC,
              SYSID: this.SYSID
            },
          }
        ]
      }
      let updateIncident = await this.dataService.UpadteIncident(data);
      if (updateIncident.type == ResultType.success) {
        this.modalController.dismiss({ updated: "ture" });
        await this.dismissLoadingController();
        await this.displaySuccessAlert(this.translate.instant("Updated") + " " + this.selectedIncidentData.INCIDENT_NO + " " + this.translate.instant("incident successfully in SAP."));
      } else {
        await this.dismissLoadingController();
        if (updateIncident.message) {
          this.errorMessage = updateIncident.message;
        }
        if (updateIncident.InfoMessage) {
          this.errorMessage = this.dataService.handleInfoMessage(updateIncident);
        }
        // await this.dataService.displayAlert(this.errorMessage);
      }
    }
    finally {
      await this.dismissLoadingController();
    }
  }

  cancel() {
    this.modalController.dismiss();
  }

  removeErrorMessage() {
    this.errorMessage = "";
  }

  async pleaseWaitLoader() {
    await this.dataService.pleaseWaitLoader();
  }

  async dismissLoadingController() {
    await this.dataService.dismissLoadingController();
  }

  async displaySuccessAlert(msg: string) {
    const alert = await this.alertController.create({
      header: this.translate.instant('Info'),
      message: msg,
      buttons: [{ text: 'Ok' }],
    });
    await alert.present();
  }

}
