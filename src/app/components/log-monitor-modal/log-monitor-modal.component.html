<ion-header>
  <ion-toolbar color="lightBlue">
    <ion-title>
      Update Incident - {{selectedIncidentData.INCIDENT_NO}}
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-header *ngIf="errorMessage.length > 0 && !errorMessage.includes('Incorrect user credentials')">
  <ion-toolbar style="--background: #fffee0">
    <div class="errorMessage-alignment" style="margin-top: 0;">
      <small class="text-danger">{{errorMessage}}</small>
      <span (click)="removeErrorMessage()" class="close-button">
        <ion-icon name="close-outline"></ion-icon>
      </span>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- Is this safety item completed? -->
  <div class="row radioSection">
    <div class="col-sm">
      <p class="p-tag-header">{{"Is this safety item completed" | translate}}<span class="text-danger">*</span></p>
      <ion-radio-group [(ngModel)]="causeFields.CLOSE_OUT" [ngModelOptions]="{standalone: true}">
        <ion-radio mode="md" value="X"></ion-radio>
        &nbsp;&nbsp;
        <ion-label for="yes" style="vertical-align: text-bottom;">{{"Yes" | translate}}</ion-label>
        &nbsp;&nbsp;
        <ion-radio mode="md" value=""></ion-radio> &nbsp;&nbsp;
        <ion-label for="no" style="vertical-align: text-bottom;">{{"No" | translate}}</ion-label>
      </ion-radio-group>
    </div>
  </div>
  <div *ngIf="(causeFields.CLOSE_OUT == 'unchecked')" style="margin-left: 5px;">
    <small class="text-danger radioSection">{{"Safety item completed is required" | translate}}</small>
  </div>

  <!-- Cause for OTH -->
  <div class="row dropDownSection" style="padding-top: 15px" *ngIf="selectedIncidentData.INCIDENT_TYPE != 'ENV'">
    <ion-row>
      <ion-col>
        <ion-label class="p-tag-header">{{"Cause" | translate}}
        </ion-label>
        <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selectedCause" value="selectedCause"
          #selectedCauseNgModel="ngModel" name="selectedCauseNgModel" required>
          <option disabled value="" selected>{{"Select Cause" | translate}}</option>
          <option *ngFor="let itemOth of othCause" value="{{itemOth.CAUSE}}">
            {{itemOth.CAUSE_DESC}}</option>
        </select>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col>
        <ion-label class="p-tag-header">{{"Corrective/ Preventative Action Taken" | translate}}
        </ion-label>
        <ion-textarea wrap="soft" rows="3" placeholder="{{ 'Corrective/ Preventative Action Taken' | translate }}"
          [(ngModel)]="causeFields.CAUSE_DESC" style="border: 1px solid black;border-radius: 5px;margin-top: 0px;"
          #detailsOfCauseNgModel="ngModel" name="detailsOfCauseNgModel" maxlength="120" required="true">
        </ion-textarea>
      </ion-col>
    </ion-row>
  </div>

  <!-- Cause for ENV -->
  <div class="row dropDownSection" style="padding-top: 15px" *ngIf="selectedIncidentData.INCIDENT_TYPE == 'ENV'">
    <ion-row>
      <ion-col>
        <ion-label class="p-tag-header">{{"Cause" | translate}}
        </ion-label>
        <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selectedCause" value="selectedCause"
          #selectedCauseNgModel="ngModel" name="selectedCauseNgModel" required>
          <option disabled value="" selected>{{"Select Cause" | translate}}</option>
          <option *ngFor="let itemEnv of envCause" value="{{itemEnv.CAUSE}}">
            {{itemEnv.CAUSE_DESC}}</option>
        </select>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col>
        <ion-label class="p-tag-header">{{"Cause Description" | translate}}
        </ion-label>
        <ion-textarea wrap="soft" rows="3" placeholder="{{ 'Cause Description' | translate }}"
          [(ngModel)]="causeFields.CAUSE_DESC" style="border: 1px solid black;border-radius: 5px;margin-top: 0px;"
          #detailsOfCauseNgModel="ngModel" name="detailsOfCauseNgModel" maxlength="120" required="true">
        </ion-textarea>
      </ion-col>
    </ion-row>
  </div>

  <div style="text-align: center; padding-top: 15px;">
    <ion-button mode="md" color="lightBlue" (click)="cancel()">&nbsp;&nbsp;Cancel&nbsp;&nbsp;</ion-button>
    <span style="padding-left:30px;"> </span>
    <ion-button mode="md" color="lightBlue" (click)="save()" [disabled]="(causeFields.CLOSE_OUT == 'unchecked')">&nbsp;
      Save &nbsp; </ion-button>
  </div>

</ion-content>