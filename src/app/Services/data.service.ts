import { Injectable, NgZone, OnInit } from '@angular/core';
import {
  RequestType,
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Alert<PERSON>ontroller, ModalController, ToastController, LoadingController } from '@ionic/angular';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { NavigationExtras, Router } from '@angular/router';
import { AppConstants } from '../Constants/app-constants';
import { Platform } from '@ionic/angular';
import { Idle, DEFAULT_INTERRUPTSOURCES } from '@ng-idle/core';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})

export class DataService implements OnInit {
  public isLogout = new BehaviorSubject(false);
  public isNetworkConnected = false;
  public isProfileCallIsDone = false;
  public firstTimeFlag = false;
  public INPUT_SYSID: any;
  public SYSID = "";
  public platform: string;

  private idleState: string = '';
  private timedOut: boolean = false;
  private idleSubscriptions: Subscription = new Subscription();
  private loading: HTMLIonLoadingElement | null = null;

  constructor(
    public unviredSDK: UnviredCordovaSDK,
    public alertController: AlertController,
    public router: Router,
    public ngZone: NgZone,
    private toastController: ToastController,
    private plt: Platform,
    private idle: Idle,
    private modalController: ModalController,
    private translate: TranslateService,
    public loadingController: LoadingController
  ) {
    this.isNetworkConnected = navigator.onLine;
    window.addEventListener('offline', () => {
      this.isNetworkConnected = false;
    });

    window.addEventListener('online', () => {
      this.isNetworkConnected = true;
    });

  }

  initializeIdleWatcher(idleTime: number = 480, timeoutPeriod: number = 120) {
    // Set idle time and timeout period
    this.idle.setIdle(idleTime); // Idle time in seconds
    this.idle.setTimeout(1); // Timeout period in seconds
    this.idle.setInterrupts(DEFAULT_INTERRUPTSOURCES);

    // Unsubscribe from previous subscriptions to prevent duplicates
    this.idleSubscriptions.unsubscribe();
    this.idleSubscriptions = new Subscription();

    // Subscribe to idle events
    this.idleSubscriptions.add(
      this.idle.onIdleEnd.subscribe(() => {
        this.idleState = 'No longer idle.';
        this.resetIdleWatcher();
      })
    );

    this.idleSubscriptions.add(
      this.idle.onTimeout.subscribe(async () => {
        this.idleState = 'Timed out!';
        this.timedOut = true;
        let hidden = false
        if (document.visibilityState === "hidden") {
          console.log('Browser is minimized or the tab is inactive.');
          this.stopIdleWatcher();
          hidden = true
          console.log('Logged out due to inactivity');
          await this.logout();
          this.sessionExpiredAlert()
          await this.modalController.dismiss()
        } else if (document.visibilityState === "visible") {
          console.log("Browser is active and the tab is visible.");
          setTimeout(async () => {
            if (!this.idleState.includes('Idle stopped.')) {
              this.stopIdleWatcher();
              alert.dismiss();
              console.log('Logged out due to inactivity');
              await this.logout();
              this.sessionExpiredAlert()
              await this.modalController.dismiss()
            }
          }, timeoutPeriod * 1000); // Adjust logout time dynamically
        }

        const alert = await this.alertController.create({
          header: this.translate.instant('Session Expiring Soon'),
          message: this.translate.instant('Your session is about to expire due to inactivity. To continue, please click Extend Session'),
          buttons: [
            {
              text: this.translate.instant('Extend Session'),
              role: 'cancel',
              handler: () => {
                this.resetIdleWatcher();
                this.idleState = 'Idle stopped.';
                alert.dismiss();
              },
            },
          ],
          backdropDismiss: false,
        });
        await alert.present();

        if (hidden) {
          await alert.dismiss();
        }
      })
    );

    this.idleSubscriptions.add(
      this.idle.onIdleStart.subscribe(() => {
        this.idleState = 'You’ve gone idle!';
      })
    );

    this.idleSubscriptions.add(
      this.idle.onTimeoutWarning.subscribe((countdown) => {
        this.idleState = 'You will time out in ' + countdown + ' seconds!';
      })
    );

    // Start idle monitoring
    this.resetIdleWatcher();
  }

  resetIdleWatcher() {
    this.idle.watch();
    this.timedOut = false;
  }

  stopIdleWatcher() {
    this.idle.stop();
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.idleSubscriptions.unsubscribe();
  }

  async sessionExpiredAlert() {
    const alert = await this.alertController.create({
      header: this.translate.instant('Session Expired'),
      message: this.translate.instant('Your session has expired due to inactivity'),
      buttons: [
        {
          text: this.translate.instant('Login'),
          handler: () => {
          }
        }
      ]
    });

    await alert.present();
  }


  ngOnInit(): void {
  }

  async checkPlatform() {
    let checkPlatform = this.plt.platforms();
    console.log("Platform information", checkPlatform);
    if (checkPlatform.includes("desktop")) {
      this.platform = "desktop"
    } else {
      this.platform = "nonDesktop"
    }
    console.log("current platform", this.platform);
    return this.platform;
  }

  async logout() {
    let inputdataOld = JSON.parse(localStorage.getItem('querydata'));
    localStorage.clear();
    localStorage.setItem('querydata', JSON.stringify(inputdataOld));
    this.setProfileCallIsDone(false);
    await this.unviredSDK.logout();
    this.ngZone.run(() => {
      const dataToSend: NavigationExtras = {
        queryParams: {
          type: 0,
        },
      };
      this.router.navigate(['login']);
    });
  }

  getLogoutStatus(): Observable<boolean> {
    return this.isLogout.asObservable();
  }

  async clearData() {
    await this.unviredSDK.logout();
  }

  checkStringAndConvertIntoNumber(data: any): number {
    let num: number;
    switch (true) {
      case typeof data === 'string':
        num = +data;
        break;
      case typeof data === 'number':
        num = data;
        break;
    }
    return num;
  }

  async displayAlert(msg: string) {
    if (msg.includes("Incorrect user credentials")) {
      await this.unviredSDK.setClientCredentials([]);
      const alert = await this.alertController.create({
        header: 'Error',
        message: msg,
        backdropDismiss: false,
        buttons: [
          {
            text: 'OK',
            role: 'confirm',
            handler: () => {
              this.logout();
              this.ngZone.run(() => this.router.navigate(['login']));
            },
          },
        ],
      });
      await alert.present();
    } else {
      const alert = await this.alertController.create({
        header: 'Alert',
        message: msg,
        buttons: [{ text: 'Ok' }],
      });
      await alert.present();
    }

  }

  // Get Data from Local DB
  async getData(headerName: string, whereCondition?: any) {
    if (whereCondition) {
      if (whereCondition.includes('ORDER BY')) {
        var result = await this.unviredSDK.dbExecuteStatement(
          `SELECT * FROM ${headerName}` + whereCondition
        );
      } else {
        var result = await this.unviredSDK.dbExecuteStatement(
          `SELECT * FROM ${headerName} WHERE ` + whereCondition
        );
      }
    } else {
      var result = await this.unviredSDK.dbExecuteStatement(
        `SELECT * FROM ${headerName}`
      );
    }
    if (result.type == ResultType.success) {
      return result.data;
    } else {
      this.unviredSDK.logError(
        'dataService',
        'getData',
        `Error while fetching ${headerName} Data - ${result.message}`
      );
    }
  }


  handleInfoMessage(result) {
    let infoMessage = '';
    if (
      result &&
      result.data &&
      result.data.InfoMessage &&
      result.data.InfoMessage.length > 0
    ) {
      for (var info of result.data.InfoMessage) {
        if (info.category != 'SUCCESS') {
          if (infoMessage) infoMessage = infoMessage + '\n';
          infoMessage = infoMessage + info.message;
        }
      }
      if (result.InfoMessage) {
        for (var info of result.InfoMessage) {
          if (info.category != 'SUCCESS') {
            if (infoMessage) infoMessage = infoMessage + '\n';
            infoMessage = infoMessage + info.message;
          }
        }
      }
    }
    return infoMessage;
  }

  handleInfoMessage1(result) {
    let infoMessage = '';
    if (
      result &&

      result.InfoMessage &&
      result.InfoMessage.length > 0
    ) {

      if (result.InfoMessage) {
        for (var info of result.InfoMessage) {
          if (info.category != 'SUCCESS') {
            if (infoMessage) infoMessage = infoMessage + '\n';
            infoMessage = infoMessage + info.message;
          }
        }
      }
    }
    return infoMessage;
  }

  async getMasterData() {
    this.INPUT_SYSID = JSON.parse(localStorage.getItem('querydata'));
    console.log("INPUT_SYSID", this.INPUT_SYSID.SYSID);
    this.SYSID = this.INPUT_SYSID.SYSID;
    console.log("SYSID", this.SYSID);
    let customInput = {
      INPUT_PLANT: [
        {
          INPUT_PLANT_HEADER: {
            PLANT: localStorage.getItem('selPlant'),
            LANG: localStorage.getItem('selLanguage'),
            SYSID: this.SYSID
          }
        }
      ]
    }
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        customInput,
        AppConstants.PA_GET_MASTERDATA,
        true
      );
      if (result.type == ResultType.success) {
        // 
        this.unviredSDK.logInfo(
          'DataService',
          'getMasterData',
          'Master data has retrived successfully.'
        );
      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'getMasterData',
          'Error occured while getting Master data'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getMasterData',
          'Error occured while getting Master data - ' + result.message
        );
        this.displayAlert(result.message);
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getMasterData',
        'Error occured while getting Master data - ' + error
      );
      return;
    }
  }

  async getProfile(data: any) {
    try {
      let customInput = {
        INPUT_SYSID: [
          {
            INPUT_SYSID_HEADER: {
              SYSID: data.SYSID
            }
          }
        ]
      }
      let result = await this.unviredSDK.syncForeground(RequestType.PULL, '', customInput, AppConstants.PA_GET_PROFILE, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getProfile',
          'getProfile'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'getProfile',
          'Error while getting profile data.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getProfile',
          'Error while getting getting profile data:' + result.message
        );
        this.displayAlert(result.message);
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getProfile',
        'getProfile' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }
  async getEmployee(empNo: any, fname: string, lname: string) {
    this.INPUT_SYSID = JSON.parse(localStorage.getItem('querydata'));
    console.log("INPUT_SYSID", this.INPUT_SYSID.SYSID);
    this.SYSID = this.INPUT_SYSID.SYSID;
    console.log("SYSID", this.SYSID);
    try {
      let customInput = {
        EMPLOYEE: [
          {
            EMPLOYEE_HEADER: {
              EMPLOYEE: empNo,
              FNAME: fname,
              NACHN: lname,
              PLANT: localStorage.getItem('selPlant'),
              SYSID: this.SYSID
            }
          }
        ]
      }
      let result = await this.unviredSDK.syncForeground(RequestType.PULL, '', customInput, AppConstants.PA_LOOKUP_EMPLOYEE, false);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getEmployee',
          'get Employee'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'getEmployee',
          'Error while getting Employee Details.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getEmployee',
          'Error while getting Employee Details.:' + result.message
        );
        this.displayAlert(result.message);
      }
      return result.data;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getEmployee',
        'Error while getting Employee Details. ' + error
      );
      let err = { type: ResultType.error, message: error };
      return err;
    }
  }

  async createSafetyIncident(customInput: any) {
    let result;
    try {
      result = await this.unviredSDK.syncForeground(RequestType.QUERY, '', customInput, AppConstants.PA_CREATE_INCIDENT, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'createSafetyIncident',
          'create Safety Incident'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'createSafetyIncident',
          'Error while getting create Safety Incident.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'createSafetyIncident',
          'Error while create Safety Incident.:' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'createSafetyIncident',
        'Error while getting create Safety Incident. ' + error
      );
      let err = { type: ResultType.error, message: error };
      return result;
    }
  }

  async createSafetyConversation(customInput: any) {
    let result;
    try {
      result = await this.unviredSDK.syncForeground(RequestType.QUERY, '', customInput, AppConstants.PA_CREATE_CONVERSATION, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'createSafetyConversation',
          'create Safety Conversation'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'createSafetyConversation',
          'Error while getting create Safety Conversation.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'createSafetyConversation',
          'Error while create Safety Conversation.:' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'createSafetyConversation',
        'Error while getting create Safety Conversation. ' + error
      );
      let err = { type: ResultType.error, message: error };
      return result;
    }
  }
  async createSafetyObservation(customInput: any) {
    let result;
    try {
      result = await this.unviredSDK.syncForeground(RequestType.QUERY, '', customInput, AppConstants.PA_CREATE_OBSERVATION, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'createSafetyObservation',
          'create Safety Observation'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'createSafetyObservation',
          'Error while getting create Safety Observation.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'createSafetyObservation',
          'Error while create Safety Observation.:' + result.message
        );
        this.displayAlert(result.message);
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'createSafetyObservation',
        'Error while getting create Safety Observation. ' + error
      );
      let err = { type: ResultType.error, message: error };
      return result;
    }
  }

  async getAttachments(customInput: any) {
    let result;
    try {
      result = await this.unviredSDK.syncForeground(RequestType.QUERY, '', customInput, AppConstants.PA_GET_INCIDENT_ATTACHMENTS, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getAttachments',
          'get Attachments'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'getAttachments',
          'Error while getting Upadte Incident.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getAttachments',
          'Error while Upadte Incident.:' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getAttachments',
        'Error while getting Upadte Incident. ' + error
      );
      let err = { type: ResultType.error, message: error };
      return result;
    }
  }

  async UpadteIncident(customInput: any) {
    let result;
    try {
      result = await this.unviredSDK.syncForeground(RequestType.QUERY, '', customInput, AppConstants.PA_UPDATE_INCIDENT, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'UpadteIncident',
          'Upadte Incident'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'UpadteIncident',
          'Error while getting Upadte Incident.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'createSafetyIncident',
          'Error while Upadte Incident.:' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'createSafetyIncident',
        'Error while getting Upadte Incident. ' + error
      );
      let err = { type: ResultType.error, message: error };
      return result;
    }
  }

  async getIncidents() {
    this.INPUT_SYSID = JSON.parse(localStorage.getItem('querydata'));
    console.log("INPUT_SYSID", this.INPUT_SYSID.SYSID);
    this.SYSID = this.INPUT_SYSID.SYSID;
    console.log("SYSID", this.SYSID);
    let customInput = {
      INPUT_PLANT: [
        {
          INPUT_PLANT_HEADER: {
            PLANT: localStorage.getItem('selPlant'),
            LANG: localStorage.getItem('selLanguage'),
            SYSID: this.SYSID
          }
        }
      ]
    }
    try {
      let result = await this.unviredSDK.syncForeground(RequestType.QUERY, '', customInput, AppConstants.PA_GET_INCIDENTS, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getIncidents',
          'get Incidents'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'getIncidents',
          'Error while getting Incidents.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getIncidents',
          'Error while getting Incidents.:' + result.message
        );
        this.displayAlert(result.message);
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getIncidents',
        'Error while getting Incidents.. ' + error
      );
      let err = { type: ResultType.error, message: error };
      return;
    }
  }

  async getInstancePort() {
    try {
      let result = await this.unviredSDK.syncForeground(RequestType.QUERY, '', '', AppConstants.PA_GET_INSTANCE_PORT_NAMES, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getInstancePort',
          'get Instance'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'getInstancePort',
          'Error while getting Instance.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getInstancePort',
          'Error while getting Instance.:' + result.message
        );
        this.displayAlert(result.message);
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getInstancePort',
        'Error while getting Instance.. ' + error
      );
      let err = { type: ResultType.error, message: error };
      return;
    }
  }

  async getInstance(headerName: string, SYSID?: any) {
    if (SYSID) {
      var result = await this.unviredSDK.dbExecuteStatement(
        `SELECT * FROM ${headerName} WHERE SYSID = "${SYSID}"`
      )
    }
    if (result.type == ResultType.success) {
      return result.data;
    } else {
      this.unviredSDK.logError(
        'dataService',
        'getData',
        `Error while fetching ${headerName} Data - ${result.message}`
      );
    }
  }

  // Toast for 2 sec to show data is proccessing in background
  async showToast(msg: string) {
    const toast = await this.toastController.create({
      message: msg,
      animated: true,
      color: 'primary',
      duration: 3000,
      position: 'middle'
    });
    toast.present();
  }

  getUMPUrl(): string {
    let hosturl = location.protocol + "//" + location.hostname;
    console.log("HOST URL____", hosturl);
    if (hosturl.toLocaleLowerCase().includes("plastipak.com") || hosturl.toLocaleLowerCase().includes("plastipak.eu")) {
      hosturl = 'https://ump.plastipak.com/UMP'
    } else {
      // hosturl = 'https://umpq.absopure.com/UMP'
      hosturl = 'https://absomi01.absopure.com:8443/UMP'
    }
    return hosturl;
  }

  getAUTHUrl(): string {
    let hosturl = location.protocol + "//" + location.hostname;
    console.log("HOST URL____", hosturl);
    if (hosturl.toLocaleLowerCase().includes("plastipak.com") || hosturl.toLocaleLowerCase().includes("plastipak.eu")) {
      hosturl = 'https://ump.plastipak.com/AUTH/login'
    } else {
      hosturl = 'https://absomi01.absopure.com:8443/AUTH/login'
    }
    return hosturl;
  }

  getHostURL(): string {
    let host = location.protocol + "//" + location.hostname;
    console.log("GET HOST URL", host);
    return host
  }

  getProfileCallIsDone() {
    return this.isProfileCallIsDone;
  }
  setProfileCallIsDone(flag) {
    this.isProfileCallIsDone = flag
  }
  getFirstTimeFlag() {
    return this.firstTimeFlag;
  }
  setFirstTimeFlag(flag) {
    this.firstTimeFlag = flag;
  }

  // Display loader
  async pleaseWaitLoader() {
    console.log("pleaseWaitLoader() called");
    if (!this.loading) {
      this.loading = await this.loadingController.create({
        message: this.translate.instant('Please wait...'),
        mode: 'ios',
        backdropDismiss: false,
      });
      await this.loading.present();
    }
  }
  
  async dismissLoadingController() {
    console.log("dismissLoadingController() called");
    if (this.loading) {
      await this.loading.dismiss();
      this.loading = null;
    }
  }

}
