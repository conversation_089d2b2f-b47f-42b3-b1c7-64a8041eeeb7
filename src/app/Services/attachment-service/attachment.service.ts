import {
  HttpClient,
  HttpHeaders,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  RequestType,
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants, EhsStatus } from 'src/app/Constants/app-constants';
import { DOCUMENT_ATTACHMENT } from 'src/app/dataModels/DOCUMENT_ATTACHMENT';
import { DataService } from '../data.service';

export enum AttachmentDownloadStatus {
  NoneAvailable,
  Pending,
  ReadyForDownload,
  Error,
}

@Injectable({
  providedIn: 'root'
})
export class AttachmentService {
  private win: any = window;

  constructor(
    public unviredSDK: UnviredCordovaSDK,
    private httpClient: HttpClient,
    private dataService: DataService,
  ) { }

  // Upload attachment Headers
  // async uploadAttachment(inputHeader: any, type?) {
  //   try {
  //     let result;
  //     if (
  //       type == EhsStatus.IN_PROCESS ||
  //       type == EhsStatus.IN_REPAIR
  //     ) {
  //       let inspectionHeaderInput = {
  //         DOCUMENT_HEADER: inputHeader,
  //       };
  //       result = await this.unviredSDK.syncBackground(
  //         RequestType.RQST,
  //         inspectionHeaderInput,
  //         '',
  //         AppConstants.PA_MODIFY_DOCUMENT,
  //         'DOCUMENT',
  //         inputHeader.LID,
  //         false
  //       );
  //     } else {
  //       result = await this.unviredSDK.syncForeground(
  //         RequestType.RQST,
  //         '',
  //         inputHeader,
  //         AppConstants.PA_MODIFY_DOCUMENT,
  //         true
  //       );
  //     }
  //     if (result.type == ResultType.success) {
  //       this.unviredSDK.logInfo(
  //         'AttachmentsService',
  //         'uploadAttachment',
  //         'Uploaded file successfully.'
  //       );
  //       this.unviredSDK.dbSaveWebData();
  //     } else if (result.code && result.code === 401) {
  //       this.unviredSDK.logError(
  //         'AttachmentsService',
  //         'uploadAttachment',
  //         'Error while Uploaded file.'
  //       );
  //     } else {
  //       this.unviredSDK.logError(
  //         'AttachmentsService',
  //         'uploadAttachment',
  //         'Error while Uploaded file : ' + result.message
  //       );
  //     }
  //     return result;
  //   } catch (error) {
  //     this.unviredSDK.logError(
  //       'AttachmentsService',
  //       'uploadAttachment',
  //       'Error while Uploaded file : ' + error
  //     );
  //     let err = { type: ResultType.error, error: error };
  //     return err;
  //   }
  // }

  // Upload file as attachment to  server
  public uploadFileToServer(file: any): Promise<any> {
    debugger
    let umpUrl = this.dataService.getUMPUrl();
    let token = `Bearer ${localStorage.getItem('EHS_token')}`;

    let headers = new HttpHeaders();
    headers = headers.append('Authorization', token);

    let formData = new FormData();
    formData.append('file', file);

    let guid = this.unviredSDK.guid();
    guid = guid.replace(/-/g, '');
    return this.httpClient
      .post(
        umpUrl + '/' +
        AppConstants.ATTACHMENT_URL +
        AppConstants.APPLICATION_NAME +
        '/attachments/' +
        guid,
        formData,
        { headers: headers }
      )
      .toPromise();
  }
}
