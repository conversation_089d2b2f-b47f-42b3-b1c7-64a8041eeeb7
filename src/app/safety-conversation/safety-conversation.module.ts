import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SafetyConversationPageRoutingModule } from './safety-conversation-routing.module';

import { SafetyConversationPage } from './safety-conversation.page';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SafetyConversationPageRoutingModule,
    NgxDropzoneModule,
    TranslateModule
  ],
  declarations: [SafetyConversationPage]
})
export class SafetyConversationPageModule {}
