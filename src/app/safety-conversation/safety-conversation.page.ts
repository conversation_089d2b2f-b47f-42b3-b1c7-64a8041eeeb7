import { Component, ElementRef, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>er, IonSelect, PopoverController } from '@ionic/angular';
import { format, parseISO, getDate, getMonth, getYear } from 'date-fns';
import { DataService } from '../services/data.service';
import * as moment from 'moment';
import { TranslateService } from '@ngx-translate/core';
import { AttachmentService } from 'src/app/Services/attachment-service/attachment.service'
import { DOCUMENT_ATTACHMENT } from 'src/app/dataModels/DOCUMENT_ATTACHMENT';
import { Platform } from '@ionic/angular';

@Component({
  selector: 'app-safety-conversation',
  templateUrl: './safety-conversation.page.html',
  styleUrls: ['./safety-conversation.page.scss'],
})
export class SafetyConversationPage implements OnInit {
  @ViewChild('mySelect') selectRef: IonSelect;
  @ViewChild('mySelectInitiator') selectRefInitiator: IonSelect;

  public selIncidentType = "";
  public selworkarea = "";
  public selAccidentLocationList = "";
  public selflocLocation = ""
  public searchEmpType = ""
  public empType = ""
  public selInjuredBodyPart = ""
  public selInjuryPart = ""
  public selTreatmentAction = ""
  public detailsOfIncident = ""
  public files: File[];
  public postMultimedias = [];
  public selDateTimeConversation: any;
  public workareafIncident = [];
  public incidentType = [];
  public injuryList = [];
  public flocLocationList = [];
  public injuredBodyPartList = [];
  public accidentLocationList = [];
  public treatmentActionList = [];
  public errorMessage: string = "";
  public selempType: string = "";
  public dateValue = "";
  public selEmpNo: string;
  public isHideBodyInjury: boolean = false;
  public isClicked: boolean = false;
  public isMobileView: boolean = false;
  public maxValue;
  public searchFname = "";
  public searchLname = "";
  public searchEmployeeList = [];
  public showEmpErr: boolean = false;
  public isCancelDateTime: boolean = false;
  public significantActivity = "";
  public safetyConversation = "";
  public conversationtopics = [];
  public conversationTypes = [];
  public Initiator = ""
  public conversationTopic = ""
  public conversationType = ""
  public searchFnameIniator = ""
  public searchLnameIniator = ""
  public searchEmpTypeIniator = ""
  public selEmpNoInitiator = "";
  public empValue = "";
  public tempValue = "";
  public contractorValue = ""
  public selLang = "en";
  public customAlertOptions = {}
  public totalFileSize = 0;
  public INPUT_SYSID: any;
  public SYSID = "";
  public newAttachment;
  public allAttachment = [];
  public userFullname = [];
  public fileURL: any[] = [];
  public platform: string;

  constructor(
    public dataService: DataService,
    private ngZone: NgZone,
    public router: Router,
    public alertController: AlertController,
    public popCtrl: PopoverController,
    private translate: TranslateService,
    private attachmentService: AttachmentService,
    private plt: Platform
  ) {
    this.files = [];
    this.maxValue = moment().format();
  }

  async ngOnInit() {
    console.log("ngOnInit called");
    // console.log("Current platform", this.plt.platforms());
    // let checkPlatform = this.plt.platforms();
    // this.platform = checkPlatform[1];
    // console.log("checkPlatform", checkPlatform);
    this.platform = await this.dataService.checkPlatform();
    console.log("current platform", this.platform);
    this.INPUT_SYSID = JSON.parse(localStorage.getItem('querydata'));
    console.log("INPUT_SYSID", this.INPUT_SYSID.SYSID);
    let selectedPlant = localStorage.getItem('selPlant');
    console.log("selectedPlant", selectedPlant);
    // let userDetailsData = await this.dataService.getData('USER_DETAILS_HEADER');
    // console.log("userDetailsData[0].PLANT", userDetailsData[0].PLANT);
  }

  async ionViewWillEnter() {
    console.log("ionViewWillEnter called")
    this.userLable();
    this.selempType = this.translate.instant("Employee");
    this.empType = this.translate.instant("Employee");
    this.empValue = this.translate.instant("Employee");
    // this.tempValue = this.translate.instant("TEMP");
    // this.contractorValue = this.translate.instant("CONTRACTOR");

    this.tempValue = "TEMP";
    this.contractorValue = "CONTRACTOR";

    this.selLang = localStorage.getItem('selLanguage');
    this.selDateTimeConversation = moment(new Date().toISOString()).locale(this.selLang).format();
    this.customAlertOptions = {
      header: this.translate.instant('Click to select an Employee'),
      translucent: true,
    };

    this.resizeScreen();
    this.ngZone.run(() => {
      window.onresize = () => {
        this.resizeScreen();
      };
    });
    await this.loadData();
  }
  resizeScreen() {
    if (window.innerWidth < 650) {
      this.isMobileView = true;
    } else {
      this.isMobileView = false;
    }
  }
  async loadData() {
    try {
      await this.pleaseWaitLoader();
      let workareaRes = await this.dataService.getData('WORKAREAS_HEADER', ' ORDER BY WORKAREA_DESC');
      if (workareaRes.length > 0) {
        this.workareafIncident = workareaRes;
      } else {
        console.log("no workarea found");
      }

      let conversationtopicsRes = await this.dataService.getData('CONVERSATION_TOPIC_HEADER');
      if (conversationtopicsRes.length > 0) {
        this.conversationtopics = conversationtopicsRes;
      } else {
        console.log("no conversation topics found");
      }

      let conversationTypeRes = await this.dataService.getData('CONVERSATION_TYPE_HEADER');
      if (conversationTypeRes.length > 0) {
        this.conversationTypes = conversationTypeRes;
      } else {
        console.log("No conversiton type found")
      }
    }
    finally {
      await this.dismissLoadingController();
    }
  }

  searchByEmp(ev) {
    this.searchFname = "";
    this.searchLname = "";

  }
  searchByFname() {
    // this.searchFname = this.searchFname.toUpperCase();
    this.searchEmpType = ""
    this.searchLname = "";
  }
  searchByLname() {
    // this.searchLname = this.searchLname.toUpperCase();
    this.searchEmpType = ""
    this.searchFname = "";

  }
  searchByEmpInitator(ev) {
    this.searchFnameIniator = "";
    this.searchLnameIniator = "";
  }
  searchByFnameInitator() {
    // this.searchFnameIniator = this.searchFnameIniator.toUpperCase();
    this.searchEmpTypeIniator = ""
    this.searchLnameIniator = "";
  }
  searchByLnameInitator() {
    // this.searchLnameIniator = this.searchLnameIniator.toUpperCase();
    this.searchEmpTypeIniator = ""
    this.searchFnameIniator = "";
  }

  formatDate(value: string) {
    if (value != "") {
      this.isCancelDateTime = false;
      let date = format(parseISO(value), 'MMM dd, yyyy h:mma');
      return date;
    } else {
      this.isCancelDateTime = true;
    }
  }

  onCancelDateTime() {
    if (this.dateValue === "") {
      this.isCancelDateTime = true;
    } else {
      this.isCancelDateTime = false;
    }
  }
  async setCompletion(event: any) {
    this.selempType = this.empType;
    this.searchEmpType = "";
    this.searchLname = "";
    this.searchFname = "";
    if (this.selempType === this.translate.instant('Employee')) {
      this.showEmpErr = true;
    } else {
      this.showEmpErr = false;
    }
  }

  selecteIncidentType() {
    this.selInjuredBodyPart = "";
    this.selInjuryPart = "";
    this.selTreatmentAction = "";
    for (let i = 0; i < this.incidentType.length; i++) {
      if (this.incidentType[i].INCI_TYPE === this.selIncidentType) {
        this.isHideBodyInjury = (this.incidentType[i].HIDE_BODY_INJURY === 'X') ? true : false;
      }
    }
  }

  async selectedWorkArea() {
    this.selAccidentLocationList = "";
    this.selflocLocation = "";
    let flocLocationRes = await this.dataService.getData('FUNC_LOC_HEADER', `WORKAREA = '${this.selworkarea}'`);
    if (flocLocationRes.length > 0) {
      this.flocLocationList = flocLocationRes;
    } else {
      this.flocLocationList = [];
      console.log("no function location found");
    }

    let accidentLocationRes = await this.dataService.getData('ACC_LOCS_HEADER', `WORKAREA = '${this.selworkarea}'`);
    if (accidentLocationRes.length > 0) {
      this.accidentLocationList = accidentLocationRes;
    } else {
      this.accidentLocationList = [];
      console.log("no accident location found");
    }
  }
  async searchEmployee() {
    if (this.empType === this.translate.instant('Employee')) {
      try {
        await this.pleaseWaitLoader();
        let empRes = await this.dataService.getEmployee(this.searchEmpType, this.searchFname.toUpperCase(), this.searchLname.toUpperCase());
        if (empRes && empRes.EMPLOYEE && empRes.EMPLOYEE.length > 0 && empRes.EMPLOYEE[0] && empRes.EMPLOYEE[0].EMPLOYEE_HEADER) {
          await this.dismissLoadingController();
          this.searchEmployeeList = [];
          if (empRes.EMPLOYEE.length === 1) {
            let header = empRes.EMPLOYEE[0].EMPLOYEE_HEADER;
            this.selempType = header.FNAME + ' ' + header.NACHN + ' (' + header.EMPLOYEE + ')'
            this.selEmpNo = header.EMPLOYEE;
          } else {
            for (let i = 0; i < empRes.EMPLOYEE.length; i++) {
              this.searchEmployeeList.push(empRes.EMPLOYEE[i].EMPLOYEE_HEADER);
            }
            if (this.searchEmployeeList.length > 0 && this.selectRef) {
              this.ngZone.run(async () => {
                await this.selectRef.open();
              });
            }
          }

        } else {
          await this.dismissLoadingController();
          this.errorMessage = await this.dataService.handleInfoMessage1(empRes);
          if (this.errorMessage.length === 0) {
            this.errorMessage = this.translate.instant('No Employee Details Found.')
          }
          // await this.dataService.displayAlert(this.translate.instant('No Employee Details Found.'));
        }
        this.showEmpErr = true;
      }
      finally {
        await this.dismissLoadingController();
      }
    } else {
      this.showEmpErr = false
    }
  }
  async searchEmployeeInitiator() {
    // if (this.empType === 'Employee') {
    try {
      await this.pleaseWaitLoader();
      let empRes = await this.dataService.getEmployee(this.searchEmpTypeIniator, this.searchFnameIniator.toUpperCase(), this.searchLnameIniator.toUpperCase());
      if (empRes && empRes.EMPLOYEE && empRes.EMPLOYEE.length > 0 && empRes.EMPLOYEE[0] && empRes.EMPLOYEE[0].EMPLOYEE_HEADER) {
        await this.dismissLoadingController();
        this.searchEmployeeList = [];
        if (empRes.EMPLOYEE.length === 1) {
          let header = empRes.EMPLOYEE[0].EMPLOYEE_HEADER;
          this.Initiator = header.FNAME + ' ' + header.NACHN + ' (' + header.EMPLOYEE + ')'
          this.selEmpNoInitiator = header.EMPLOYEE;
        } else {
          for (let i = 0; i < empRes.EMPLOYEE.length; i++) {
            this.searchEmployeeList.push(empRes.EMPLOYEE[i].EMPLOYEE_HEADER);
          }
          if (this.searchEmployeeList.length > 0 && this.selectRefInitiator) {
            this.ngZone.run(async () => {
              await this.selectRefInitiator.open();
            });
          }
        }

      } else {
        await this.dismissLoadingController();
        this.errorMessage = await this.dataService.handleInfoMessage1(empRes);
        if (this.errorMessage.length === 0) {
          this.errorMessage = this.translate.instant('No Employee Details Found.');
        }
      }
    }
    finally {
      await this.dismissLoadingController();
    }

  }
  setEmployee(ev) {
    console.log("" + this.selempType)
    if (this.empType === this.translate.instant('Employee')) {
      for (let i = 0; i < this.searchEmployeeList.length; i++) {
        if (this.searchEmployeeList[i].EMPLOYEE === this.selEmpNo) {
          this.selempType = this.searchEmployeeList[i].FNAME + ' ' + this.searchEmployeeList[i].NACHN + ' (' + this.searchEmployeeList[i].EMPLOYEE + ')';
        }
      }
    }

  }
  setEmployeeInitator(ev) {
    console.log("" + this.selempType)
    for (let i = 0; i < this.searchEmployeeList.length; i++) {
      if (this.searchEmployeeList[i].EMPLOYEE === this.selEmpNoInitiator) {
        this.Initiator = this.searchEmployeeList[i].FNAME + ' ' + this.searchEmployeeList[i].NACHN + ' (' + this.searchEmployeeList[i].EMPLOYEE + ')';
      }
    }
  }

  previewFile(event: any) {
    if (event.target.files[0]) {
      this.files.push(event.target.files[0]);
    }
    let reader = new FileReader();
    if (event.target.files[0]) {
      reader.readAsDataURL(event.target.files[0]);
    }
    reader.onload = (_event) => {
      this.fileURL.push(<string>reader.result)
      console.log("mobile file preview data", this.fileURL);
    }
  }

  deleteImage(index: number) {
    this.fileURL.splice(index, 1);
    this.files.splice(index, 1);
  }

  // Adding Attachments 
  async onSelect(event) {
    if (event.addedFiles.type === "image/jpeg") {
    }
    this.files.push(...event.addedFiles);
  }

  // Upload file to server
  async saveFile() {
    for (var i = 0; i < this.files.length; i++) {
      await this.attachmentService.uploadFileToServer(this.files[i]).then(async (result) => {
        let documetAttachmentHeader = this.createDocumentAttachmentHeader(result.attachmentId, this.files[i]);
        this.allAttachment.push(documetAttachmentHeader);
      }, (error) => {
        this.dismissLoadingController();
        this.dataService.displayAlert(this.translate.instant("Attachment upload failed, Please try again"));
      })
    }
  }

  createDocumentAttachmentHeader(attachmentUid: string, file: any): any {
    this.newAttachment = <DOCUMENT_ATTACHMENT>{};
    this.newAttachment.UID = attachmentUid;
    this.newAttachment.FILE_NAME = file.name;
    this.newAttachment.MIME_TYPE = file.type;
    this.newAttachment.ATTACHMENT_STATUS = "UPLOADED";
    return this.newAttachment;
  }

  onRemove(event) {
    let position = this.files.indexOf(event);
    this.files.splice(position, 1);
  }

  cancel() {
    if (this.selIncidentType !== "" || this.selworkarea !== "" || this.selAccidentLocationList !== "" || this.selflocLocation !== "" || this.searchEmpType !== "" || this.empType !== "" || this.selInjuredBodyPart !== "" || this.selInjuryPart !== "" || this.selTreatmentAction !== "" || this.detailsOfIncident !== "") {
      this.confirmationCancelDialog(this.translate.instant('Unsaved changes will be lost. Continue?'));
    } else {
      this.ngZone.run(() => this.router.navigate(['home']));
    }
  }

  async confirmationCancelDialog(msg: string) {
    const alert = await this.alertController.create({
      header: this.translate.instant('Warning'),
      mode: 'ios',
      cssClass: 'secondary',
      message: `<strong>${msg}</strong>`,
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => { },
        },
        {
          text: this.translate.instant('Yes'),
          cssClass: 'secondary',
          handler: async () => {
            this.ngZone.run(() => this.router.navigate(['home']));
          },
        },
      ],
    });
    await alert.present();
  }

  async createSafetyConversation() {
    try {
      await this.pleaseWaitLoader();
      this.INPUT_SYSID = JSON.parse(localStorage.getItem('querydata'));
      console.log("INPUT_SYSID", this.INPUT_SYSID.SYSID);
      this.SYSID = this.INPUT_SYSID.SYSID;
      console.log("SYSID", this.SYSID);
      await this.saveFile();
      let userDetailsData = await this.dataService.getData('USER_DETAILS_HEADER');
      let selectedPlant = localStorage.getItem('selPlant');
      if (userDetailsData.length > 0) {
        let data = {
          CONVERSATION: [
            {
              CONVERSATION_HEADER: {
                INCIDENT_NO: String(Math.floor(100000 + Math.random() * 900000)),
                PLANT: selectedPlant,
                INCIDENT_TYPE: 'ZON',
                INCIDENT_DATE: moment(new Date(this.selDateTimeConversation)).format('YYYY-MM-DD'),
                INCIDENT_TIME: moment(new Date(this.selDateTimeConversation)).format('HH:mm:ss'),
                EMP_NO: (this.empType && this.empType === this.translate.instant('Employee')) ? this.selEmpNo : this.selempType,
                WORKAREA: this.selworkarea,
                AC_LOC: "",
                FUNC_LOC: "",
                INITIATOR: this.selEmpNoInitiator,
                CONV_TOPIC: this.conversationTopic,
                CONV_TYPE: this.conversationType,
                SIGN_ACTIVITY: this.significantActivity,
                DETAIL_DESC: this.safetyConversation,
                SYSID: this.SYSID
              },
              CONVERSATION_ATTACHMENT: this.allAttachment
            }
          ]
        }
        let createSafetyConversationRes = await this.dataService.createSafetyConversation(data);
        if (createSafetyConversationRes.type == ResultType.success) {
          await this.dismissLoadingController();
          if (createSafetyConversationRes.data &&
            createSafetyConversationRes.data.CONVERSATION.length > 0 &&
            createSafetyConversationRes.data.CONVERSATION[0]
              .CONVERSATION_HEADER) {
            await this.displaySuccessAlert(this.translate.instant("Conversation") + " " + createSafetyConversationRes.data.CONVERSATION[0].CONVERSATION_HEADER.INCIDENT_NO + " " + this.translate.instant("created successfully in SAP."));
          } else {
            console.log("no workarea found");
          }
          this.ngZone.run(() => this.router.navigate(['home']));
        } else {
          await this.dismissLoadingController();
          if (createSafetyConversationRes.message) {
            this.errorMessage = createSafetyConversationRes.message;
          }
          if (createSafetyConversationRes.InfoMessage) {
            this.errorMessage = this.dataService.handleInfoMessage(createSafetyConversationRes);
          }
          await this.dataService.displayAlert(this.errorMessage);
        }
      } else {
        console.log("no user details found")
      }
    }
    finally {
      await this.dismissLoadingController();
    }
  }

  goBackToLastLocation() {
    this.ngZone.run(() => this.router.navigate(['home']));
  }
  
  async userLable() {
    let user = await this.dataService.getData('USER_DETAILS_HEADER');
    if (user.length > 0) {
      this.userFullname = user;
      for (let i = 0; i < this.userFullname.length; i++) {
        let fullname = this.userFullname[i];
        console.log(fullname); // or do something with the fullname
        if (fullname.EMP_NO == null) {
          this.Initiator = "";
        } else {
          this.Initiator = fullname.FULLNAME + '(' + fullname.EMP_NO + ')';
          this.selEmpNoInitiator = fullname.EMP_NO;
        }
      }
    } else {
      console.log("no data found");
    }
  }
  
  async pleaseWaitLoader() {
    await this.dataService.pleaseWaitLoader();
  }

  async dismissLoadingController() {
    await this.dataService.dismissLoadingController();
  }

  async displaySuccessAlert(msg: string) {
    const alert = await this.alertController.create({
      header: this.translate.instant('Info'),
      message: msg,
      buttons: [{ text: this.translate.instant('Ok') }],
    });
    await alert.present();
  }

}
