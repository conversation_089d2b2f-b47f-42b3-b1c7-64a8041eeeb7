<ion-header>
  <ion-toolbar color="lightBlue">
    <ion-buttons slot="start">
      <ion-button (click)='goBackToLastLocation()'>
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <div class="header-title">
      <ion-title>
        <span class="responsive-title" style="vertical-align: -webkit-baseline-middle !important;">{{"Safety
          Conversation" | translate}}</span>
      </ion-title>
      <ion-title class="text-center login">
        <span *ngFor="let users of userFullname" class="user-title">{{users.FULLNAME |
          titlecase }} - Logged In</span>
      </ion-title>
      <ion-title>
        <img src="assets/img/logo.png" class="mobile-responsive-logo" style="width: 125px; float: right;">
      </ion-title>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content>
  <form #Form="ngForm">
    <ion-grid style="margin-bottom: -10px !important;padding:15px;margin-left:10px;color: black !important;">

      <!-- Date and Time -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Date and Time of One on One" | translate}}<span
              class="text-danger">*</span></ion-label>
          <div style="border: 1px solid black;height: 35px;border-radius: 5px;" button="true" id="open-date-input-conv">
            <div style="padding: 8px;">
              {{dateValue}}
              <span style="float: right;">
                <ion-icon name="calendar-clear-outline" button="true" id="open-date-input-conv"></ion-icon>
              </span>
            </div>

            <ion-popover trigger="open-date-input-conv" show-backdrop="false">
              <ng-template>
                <ion-datetime #popoverDatetimeConv show-default-buttons="true" max="{{maxValue}}"
                  (ionChange)="dateValue = formatDate(popoverDatetimeConv.value);" (ionCancel)="onCancelDateTime()"
                  [(ngModel)]="selDateTimeConversation" #selDateTimeConversationNgModel="ngModel"
                  name="selDateTimeConversationNgModel" locale={{selLang}}>
                </ion-datetime>
              </ng-template>
            </ion-popover>

            <!-- <div *ngIf="selDateTimeConversationNgModel.errors && (selDateTimeConversationNgModel.dirty || selDateTimeConversationNgModel.touched)">
            <small *ngIf="selDateTimeConversationNgModel.errors.required" class="text-danger">Date and Time of Incident is required.</small>
          </div> -->
          </div>
          <div *ngIf="(selDateTimeConversation === '' && isCancelDateTime)">
            <small class="text-danger">{{"Date and Time of One on One" | translate}} {{"is required." |
              translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Injured Person Employee Number -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-radio-group [(ngModel)]="empType" (ionChange)="setCompletion($event)" style="display: inline-flex;"
            #empTypeNgModel="ngModel" name="empTypeNgModel">
            <ion-radio mode="md" slot="start" color="primary" value={{empValue}}></ion-radio>&nbsp;&nbsp;
            <ion-label>{{"Employee" | translate}}</ion-label>&nbsp;&nbsp;&nbsp;&nbsp;

            <ion-radio mode="md" slot="start" color="primary" value={{tempValue}}></ion-radio>&nbsp;&nbsp;
            <ion-label>{{"Temp" | translate}}</ion-label>&nbsp;&nbsp;&nbsp;&nbsp;

            <ion-radio mode="md" slot="start" color="primary" value={{contractorValue}}></ion-radio>&nbsp;&nbsp;
            <ion-label>{{"Contractor" | translate}}</ion-label><span class="text-danger">*</span>

          </ion-radio-group>
          <ion-input style="border: 1px solid black;border-radius: 5px;" [(ngModel)]="selempType" readonly="true"
            #selempTypeNgModel="ngModel" name="selempTypeNgModel">
          </ion-input>
        </ion-col>
      </ion-row>

      <ion-row *ngIf="empType === empValue">
        <ion-col [attr.size]="isMobileView ? 3 : 1.20">
          <ion-searchbar placeholder="{{ 'First Name' | translate}}" #searchbar [(ngModel)]="searchFname"
            style="color:black !important;" (ionInput)="searchByFname()" #searchFnameNgModel="ngModel"
            name="searchFnameNgModel"></ion-searchbar>
        </ion-col><span style="margin-top: 18px;">{{"OR" | translate}}</span>
        <ion-col [attr.size]="isMobileView ? 3 : 1.20">
          <ion-searchbar placeholder="{{ 'Last Name' | translate }}" #searchbar [(ngModel)]="searchLname"
            style="color:black !important;" (ionInput)="searchByLname()" #searchLnameNgModel="ngModel"
            name="searchLnameNgModel"></ion-searchbar>
        </ion-col>
        <ion-col [attr.size]="isMobileView ? 2 : 0.75">
          <ion-button (click)="searchEmployee()" class="footer-btn" style="margin-right: 20px !important;">
            {{"Search" | translate}}
          </ion-button>
        </ion-col>
      </ion-row>
      <div *ngIf="(selempType === empValue) && showEmpErr" style="margin-left: 5px;">
        <small class="text-gray">{{"For Wild card search - Enter ‘*’ at the end of the text." | translate}}</small>
      </div>
      <div *ngIf="(selempType === empValue) && showEmpErr" style="margin-left: 5px;">
        <small class="text-danger">{{"Employee Number is required." | translate}}</small>
      </div>

      <!-- Initiator -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Initiator" | translate}}<span class="text-danger">*</span></ion-label>
          <!-- <ion-textarea wrap="soft" rows="3" placeholder="Initiator" [(ngModel)]="Initiator" style="border: 1px solid black;border-radius: 5px;margin-top: 0px;" #InitiatorNgModel="ngModel" name="InitiatorNgModel" required="true">
            </ion-textarea> -->
          <!-- <input spellcheck="false" autocomplete="off" type="text" class="form-control" #InitiatorNgModel="ngModel" -->
          <ion-input style="border: 1px solid black;border-radius: 5px;" placeholder="{{ 'Initiator' | translate }}"
            [(ngModel)]="Initiator" readOnly="true" #InitiatorNgModel="ngModel" name="InitiatorNgModel">
          </ion-input>


          <div *ngIf="InitiatorNgModel.errors && (InitiatorNgModel.dirty || InitiatorNgModel.touched)">
            <small *ngIf="InitiatorNgModel.errors.required" class="text-danger">{{"Initiator is required." |
              translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col [attr.size]="isMobileView ? 3 : 1.20">
          <ion-searchbar placeholder="{{ 'First Name' | translate }}" #searchbar [(ngModel)]="searchFnameIniator"
            style="color:black !important;" (ionInput)="searchByFnameInitator()" #searchFnameIniatorNgModel="ngModel"
            name="searchFnameIniatorNgModel"></ion-searchbar>
        </ion-col><span style="margin-top: 18px;">{{"OR" | translate}}</span>
        <ion-col [attr.size]="isMobileView ? 3 : 1.20">
          <ion-searchbar placeholder="{{ 'Last Name' | translate }}" #searchbar [(ngModel)]="searchLnameIniator"
            style="color:black !important;" (ionInput)="searchByLnameInitator()" #searchLnameIniatorNgModel="ngModel"
            name="searchLnameIniatorNgModel"></ion-searchbar>
        </ion-col>
        <ion-col [attr.size]="isMobileView ? 2 : 0.75">
          <ion-button (click)="searchEmployeeInitiator()" class="footer-btn" style="margin-right: 20px !important;">
            {{"Search" | translate}}
          </ion-button>
        </ion-col>
      </ion-row>

      <div *ngIf="(selempType === empValue) && showEmpErr" style="margin-left: 5px;">
        <small class="text-gray">{{"For Wild card search - Enter ‘*’ at the end of the text." | translate}}</small>
      </div>
      <div *ngIf="(selempType === empValue) && showEmpErr" style="margin-left: 5px;">
        <small class="text-danger">{{"Employee Number is required." | translate}}</small>
      </div>

      <!-- Select Work Area of the Incident -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Work Area" | translate}}<span class="text-danger">*</span></ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selworkarea" value="selworkarea"
            (change)="selectedWorkArea()" #selworkareaNgModel="ngModel" name="selworkareaNgModel" required>
            <option disabled value="" selected>{{"Select Work Area" | translate}}</option>
            <option *ngFor="let item of workareafIncident" value="{{item.WORKAREA}}">
              {{item.WORKAREA_DESC}}</option>
          </select>
          <div *ngIf="selworkareaNgModel.errors && (selworkareaNgModel.dirty || selworkareaNgModel.touched)">
            <small *ngIf="selworkareaNgModel.errors.required" class="text-danger">{{"Work Area is required." |
              translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Conversation Topic -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Conversation Topic" | translate}}<span class="text-danger">*</span>
          </ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="conversationTopic"
            value="conversationTopic" (change)="selectedWorkArea()" #conversationTopicNgModel="ngModel"
            name="conversationTopicNgModel" required>
            <option value="" disabled selected>{{"Select Conversation Topic" | translate}}</option>
            <option *ngFor="let item of conversationtopics" value="{{item.CONV_TOPIC}}">
              {{item.CONV_TOPIC_DESC}}</option>
          </select>
          <div
            *ngIf="conversationTopicNgModel.errors && (conversationTopicNgModel.dirty || conversationTopicNgModel.touched)">
            <small *ngIf="conversationTopicNgModel.errors.required" class="text-danger">{{"Conversation Topic is
              required" | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Conversation Type" | translate}}<span class="text-danger">*</span>
          </ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="conversationType"
            value="conversationType" (change)="selectedWorkArea()" #conversationTypeNgModel="ngModel"
            name="conversationTypeNgModel" required>
            <option value="" disabled selected>{{"Select Conversation Type" | translate}}</option>
            <option *ngFor="let item of conversationTypes" value="{{item.CONV_TYPE}}">
              {{item.CONV_TYPE_DESC}}</option>
          </select>
          <div
            *ngIf="conversationTypeNgModel.errors && (conversationTypeNgModel.dirty || conversationTypeNgModel.touched)">
            <small *ngIf="conversationTypeNgModel.errors.required" class="text-danger">{{"Conversation Type is required"
              | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Short Description -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Short Description" | translate}}<span class="text-danger">*</span>
          </ion-label>
          <ion-input style="border: 1px solid black;border-radius: 5px;" [(ngModel)]="significantActivity"
            placeholder="{{ 'Short Description' | translate }}" #significantActivityNgModel="ngModel"
            name="significantActivityNgModel" maxLength="60" required="true">
          </ion-input>
          <div
            *ngIf="significantActivityNgModel.errors && (significantActivityNgModel.dirty || significantActivityNgModel.touched)">
            <small *ngIf="significantActivityNgModel.errors.required" class="text-danger">{{"Significant Activity is
              required." | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Safety Conversation -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Detailed Safety Conversation" | translate}}<span
              class="text-danger">*</span>
          </ion-label>
          <ion-textarea wrap="soft" rows="3" placeholder="{{ 'Detailed Safety Conversation' | translate }}"
            [(ngModel)]="safetyConversation" style="border: 1px solid black;border-radius: 5px;margin-top: 0px;"
            #safetyConversationNgModel="ngModel" name="safetyConversationNgModel" required="true">
          </ion-textarea>
          <div
            *ngIf="safetyConversationNgModel.errors && (safetyConversationNgModel.dirty || safetyConversationNgModel.touched)">
            <small *ngIf="safetyConversationNgModel.errors.required" class="text-danger">{{"Safety Conversation is
              required." | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Attachments -->
      <ion-row *ngIf="platform == 'desktop'">
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <div>
            <ion-label class="p-tag-header">{{"Attachments" | translate}}</ion-label>
            <ngx-dropzone (change)="onSelect($event)">
              <ngx-dropzone-label>
                <p>{{"Drop files here" | translate}}</p>
                <p>{{"Or" | translate}}</p>
                <p>{{"Click to browse and add" | translate}}</p>
              </ngx-dropzone-label>
              <ngx-dropzone-preview ngProjectAs="ngx-dropzone-preview" *ngFor="let f of files" [file]="f"
                [removable]="true" (removed)="onRemove(f)">
                <ngx-dropzone-label style="width: 100%;">{{ f.name }}</ngx-dropzone-label>
              </ngx-dropzone-preview>
            </ngx-dropzone>
            <div id="AttachmentError"></div>
          </div>
        </ion-col>
      </ion-row>

      <!-- Attachments for mobile view-->
      <ion-row *ngIf="platform != 'desktop'">
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <label for="files" class="btn p-tag-header">{{"Upload Attachments" | translate}}</label><br>
          <input type="file" id="fileInput" class="custom-file-input" accept="image/*" (change)="previewFile($event)"
            style="color: transparent;" capture><br>
          <label for="fileInput" class="custom-file-button">{{"Camera" | translate}}</label>

          <div class="image-container-one" *ngIf="files.length > 0">
            <div class="image-flex">
              <div id="uploadedImage" class="image-container" *ngFor="let url of fileURL; let i = index">
                <img src="{{url}}" height="200" alt="Image preview...">
                <button class="remove-button" (click)="deleteImage(i)">{{"Remove" | translate}}</button>
              </div>
            </div>
          </div>

          <div class="image-container-two" *ngIf="files.length <= 0">
            <div>
              <p class="p-tag-header">{{"No images has been captured" | translate}}</p>
            </div>
          </div>

        </ion-col>
      </ion-row>
    </ion-grid>

    <ion-list hidden="true">
      <ion-item>
        <ion-label style="text-align: center;">{{"Select Employee" | translate}}</ion-label>
        <ion-select id="customAlertSelect" [interfaceOptions]="customAlertOptions" mode="ios" interface="action-sheet"
          placeholder="{{ 'Select One' | translate }}" [(ngModel)]="selEmpNo" #mySelect
          (ionChange)='setEmployee($event)' #selEmpNoNgModel="ngModel" name="selEmpNoNgModel">
          <ion-select-option *ngFor="let item of searchEmployeeList" value="{{item.EMPLOYEE}}">{{item.FNAME}}
            {{item.NACHN}} ({{item.EMPLOYEE}})
          </ion-select-option>
        </ion-select>
      </ion-item>
    </ion-list>

    <ion-list hidden="true" class="Action-sheet-center">
      <ion-item>
        <ion-label style="text-align: center;">{{"Select Employee" | translate}}</ion-label>
        <ion-select id="customAlertSelect" [interfaceOptions]="customAlertOptions" mode="ios" interface="action-sheet"
          placeholder="{{ 'Select One' | translate }}" [(ngModel)]="selEmpNoInitiator" #mySelectInitiator
          (ionChange)='setEmployeeInitator($event)' #selEmpNoNgModel="ngModel" name="selEmpNoNgModel">
          <ion-select-option *ngFor="let item of searchEmployeeList" value="{{item.EMPLOYEE}}">{{item.FNAME}}
            {{item.NACHN}} ({{item.EMPLOYEE}})
          </ion-select-option>
        </ion-select>
      </ion-item>
    </ion-list>
  </form>
</ion-content>


<ion-footer>
  <ion-toolbar class="toolbar-bg">
    <!-- <ion-button slot="end" (click)="createSafetyConversation()" class="footer-btn" [disabled]="(selDateTimeConversation === '') || (selworkareaNgModel.errors || selworkareaNgModel.invalid) || (significantActivityNgModel.errors || significantActivityNgModel.invalid) || (safetyConversationNgModel.errors || safetyConversationNgModel.invalid) || (conversationTopicNgModel.errors || conversationTopicNgModel.invalid) || (selempType === 'Employee')">Submit -->
    <ion-button slot="end" (click)="createSafetyConversation()" class="footer-btn"
      [disabled]="(selDateTimeConversation === '') || (selworkareaNgModel.errors || selworkareaNgModel.invalid) || (significantActivityNgModel.errors || significantActivityNgModel.invalid) || (safetyConversationNgModel.errors || safetyConversationNgModel.invalid) || (conversationTopicNgModel.errors || conversationTopicNgModel.invalid) || (conversationTypeNgModel.errors || conversationTypeNgModel.invalid) ||(Initiator === '') || (significantActivity === '')">
      {{"Submit" | translate}}

    </ion-button>
    <ion-button slot="end" (click)="cancel()" class="footer-btn" style="margin-right: 20px !important;">{{"Cancel" |
      translate}}
    </ion-button>
  </ion-toolbar>
</ion-footer>