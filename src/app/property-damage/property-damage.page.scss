ion-item{
    width: 100% !important;
}

.footer-btn{
    --background: var(--ion-color-primary) !important;
    --color: var(--ion-color-primary-contrast) !important;
        border: 1px solid;
        border-radius: 5px;
        margin-right: 10px;
  }
  select{
    background: whitesmoke !important;
    
  }
  
  .p-tag-header {
    font-family: Arial;
    font-weight: 400;
    font-size: 15px;
    color: rgba(73, 73, 73, 0.76);
    font-style: normal;
    letter-spacing: 0px;
    line-height: 19px;
    text-decoration: none;
  }
  ion-searchbar{
    --background: whitesmoke !important;
    border: 1px solid black;
    border-radius: 5px;
    height: 45px;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    --box-shadow: none;
  }

  .user-title{
    float: center;
    margin-left: 35%;
    font-size: 16px;
    font-weight:100;
  }
  
  .header-title{
    display:flex;
    justify-content: space-around;
  }

  input {
    margin-top: 1rem;
}

.custom-file-button {
  display: inline-block;
  padding: 8px 12px;
  color: dodgerblue;
  border: thin solid grey;
  border-radius: 3px;
  cursor: pointer;
  margin-bottom: 1%;
}

input[type="file"] {
  display: none;
}

  .image-flex{
    display: flex;
    flex-wrap: wrap;
    justify-content:flex-start;
    gap: 10px;
  }

  .btn{
    margin-bottom: 1%;
  }

  .delete-btn{
    position: absolute;
    font-size: 22px;
  }

  .image-container-one {
    border: 1px dotted #000;
    border-radius: 1%;
    padding: 10px;
    display: flex;
    justify-content: flex-start;
    min-height: 200px; /* Set your desired minimum height */
    min-width: 200px; /* Set your desired minimum width */
  }

  .image-container-two {
    border: 1px dotted #000;
    border-radius: 1%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px; /* Set your desired minimum height */
    min-width: 200px; /* Set your desired minimum width */
  }

  .image-container {
    position: relative;
    display: inline-block;
    margin-top: 2%;
    width: 150px;
    height: 135px;
    overflow: hidden;
    border: 1px solid black;
  }

  .remove-button {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: #ff0000;
    color: #fff;
    border: none;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
  }
  
  .image-container img {
      width: 100%;
      min-height: 100%;
  }
  
  @media only screen and (max-width: 425px){
    .login {
        display: none !important;
    }
  }

  @media only screen and (max-width: 425px){
    .mobile-responsive-logo {
        display: none !important;
    }
}

@media only screen and (max-width: 425px){
  .responsive-title {
      font-size: 15px !important;
  }
}

.text-gray {
  color: gray;
}

.m-top{
  margin-top: 2px !important;
}