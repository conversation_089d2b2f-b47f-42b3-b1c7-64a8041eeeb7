import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { PropertyDamagePageRoutingModule } from './property-damage-routing.module';

import { PropertyDamagePage } from './property-damage.page';
import { TranslateModule } from '@ngx-translate/core';
import { NgxDropzoneModule } from 'ngx-dropzone';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    PropertyDamagePageRoutingModule,
    TranslateModule,
    NgxDropzoneModule
  ],
  declarations: [PropertyDamagePage]
})
export class PropertyDamagePageModule {}
