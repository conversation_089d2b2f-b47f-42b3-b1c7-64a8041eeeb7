import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { LogMonitorAttachmentsPageRoutingModule } from './log-monitor-attachments-routing.module';

import { LogMonitorAttachmentsPage } from './log-monitor-attachments.page';
import { TranslateModule } from '@ngx-translate/core';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ChipModule } from 'primeng/chip';
import { ButtonModule } from 'primeng/button';
import { RadioButtonModule } from 'primeng/radiobutton';
import { BadgeModule } from 'primeng/badge';
import { DropdownModule } from 'primeng/dropdown';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    LogMonitorAttachmentsPageRoutingModule,
    TranslateModule,
    NgxDropzoneModule,
    TableModule,
    InputTextModule,
    ChipModule,
    ButtonModule,
    RadioButtonModule,
    BadgeModule,
    DropdownModule
  ],
  declarations: [LogMonitorAttachmentsPage]
})
export class LogMonitorAttachmentsPageModule {}
