<ion-header>
  <ion-toolbar color="lightBlue">
    <ion-title *ngIf="platform == 'desktop'">
      Attachments of Incident - {{selectedIncidentData.MONITOR_HEADER.INCIDENT_NO}}
    </ion-title>
    <ion-title *ngIf="platform != 'desktop'">
      ATCH - {{selectedIncidentData.MONITOR_HEADER.INCIDENT_NO}}
    </ion-title>
    <div class="Close-button" slot="end">
      <ion-button fill="solid" mode="md" color="lightBlue" (click)="cancel()">Close</ion-button>
    </div>
  </ion-toolbar>
</ion-header>

<ion-header *ngIf="errorMessage.length > 0 && !errorMessage.includes('Incorrect user credentials')">
  <ion-toolbar style="--background: #fffee0">
    <div class="errorMessage-alignment" style="margin-top: 0;">
      <small class="text-danger">{{errorMessage}}</small>
      <span (click)="removeErrorMessage()" class="close-button">
        <ion-icon name="close-outline"></ion-icon>
      </span>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content>

  <p-table [value]="selectedIncidentData.ATTACHMENTS" responsiveLayout="scroll">
    <ng-template pTemplate="header">
        <tr>
            <th>File Name</th>
            <th>File Type</th>
            <th>Attachments</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-attachment>
        <tr>
            <td>{{attachment.NAME}}</td>
            <td>{{attachment.FILE_TYPE}}</td>
            <td>
                <a href="{{attachment.DATA}}" target="_blank">
                  <ion-button fill="outline" Type="ios">
                    View File
                  </ion-button>
                </a>
            </td>
        </tr>
    </ng-template>
</p-table>


</ion-content>
