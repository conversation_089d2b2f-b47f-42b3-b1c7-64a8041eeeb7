import { Component, OnInit } from '@angular/core';
import { ModalController  } from '@ionic/angular';
import { Platform } from '@ionic/angular';


@Component({
  selector: 'app-log-monitor-attachments',
  templateUrl: './log-monitor-attachments.page.html',
  styleUrls: ['./log-monitor-attachments.page.scss'],
})
export class LogMonitorAttachmentsPage implements OnInit {
  public selectedIncidentData : any;
  public errorMessage: string = "";
  public platform: string;

  constructor(
    public modalController: ModalController,
    private plt: Platform

  ) { }

  ngOnInit() {
    let checkPlatform = this.plt.platforms();
    console.log("Platform information", checkPlatform);
    if (checkPlatform.includes("desktop")) {
      this.platform = "desktop"
    } else {
      this.platform = "nonDesktop"
    }
    console.log("current platform", this.platform);
    console.log("All attachment", this.selectedIncidentData);
  }

  removeErrorMessage() {
    this.errorMessage = "";
  }

  cancel() {
    this.modalController.dismiss();
  }
}
