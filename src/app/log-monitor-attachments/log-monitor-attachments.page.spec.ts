import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { LogMonitorAttachmentsPage } from './log-monitor-attachments.page';

describe('LogMonitorAttachmentsPage', () => {
  let component: LogMonitorAttachmentsPage;
  let fixture: ComponentFixture<LogMonitorAttachmentsPage>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ LogMonitorAttachmentsPage ],
      imports: [IonicModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(LogMonitorAttachmentsPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
