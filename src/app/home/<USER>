.user-title{
    float: center;
    margin-left: 35%;
    font-size: 16px;
    font-weight:100;
  }

  .header-title{
    display:flex;
    justify-content: space-around;
  }

  @media only screen and (max-width: 425px){
    .login {
        display: none !important;
    }
}

@media only screen and (max-width: 425px){
    .mobile-responsive-logo {
        display: none !important;
    }
}

@media only screen and (max-width: 425px){
    .responsive-title {
        font-size: 15px !important;
        // display: none !important;
    }
}



// @media only screen and (max-width: 425px){
//     .responsive-select{
//         width: 100% !important;
//         border-radius: 5px !important;
//         height: 35px !important;
//         width: 100% !important;
//         margin-top: 5px !important;
//     }
// }