<ion-header>
  <ion-toolbar color="lightBlue">
    <ion-buttons slot="start">
      <ion-button (click)="logout()">
        <ion-icon slot="icon-only" name="log-out-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <div class="header-title">
      <ion-title>
        <span class="responsive-title" style="vertical-align: -webkit-baseline-middle !important;">{{"Environment Health
          &
          Safety Application" |
          translate}}</span>
      </ion-title>
      <ion-title class="text-center login">
        <span *ngFor="let users of userFullname" class="user-title">{{users.FULLNAME |
          titlecase }} - Logged In</span>
      </ion-title>
      <ion-title>
        <img src="assets/img/logo.png" class="mobile-responsive-logo" style="width: 125px; float: right;">
      </ion-title>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="outer-container">

    <div>
      <ion-button (click)="CreateSafetyConversation()" Expand="block" color="lightBlue">
        {{"Conversation" | translate}}
      </ion-button>
    </div><br>
    <div>
      <ion-button (click)="CreateSafetyObservation()" Expand="block" color="lightBlue">
        {{"Near Miss" | translate}} / {{"Observation" | translate}}
      </ion-button>
    </div><br>
    <div>
      <ion-button (click)="NavToPropertyDamage()" Expand="block" color="lightBlue">
        {{"Property damage" | translate}}
      </ion-button>
    </div><br>
    <div>
      <ion-button (click)="CreateEnvironmentIncident()" Expand="block" color="lightBlue">
        {{"Environmental Incident" | translate}}
      </ion-button>
    </div><br>
    <div>
      <!-- If there is data in checkIncidenType then it has been set to false -->
      <ion-button (click)="CreateSafetyIncident()" Expand="block" color="lightBlue" [disabled]="checkIncidenType">
        {{"Injury" | translate}}
      </ion-button>      
    </div><br>
    <div>
      <!-- If there is data in checkIncidenType then it has been set to false -->
      <ion-button (click)="Createincidentlogmonitor()" Expand="block" color="lightBlue" [disabled]="checkIncidenType">
        {{"Incident Log Monitor" | translate}}
      </ion-button>
    </div><br>
    <div>
      <ion-grid style="margin-left: -8px !important;">
        <ion-row>
          <ion-col size="4" style="text-align: left;">
            <ion-label>{{"Plant" | translate}}</ion-label>
            <select style="border-radius: 5px;height: 35px;width:100%;margin-top: 5px;" [(ngModel)]="selPlant"
              value="selPlant" (change)="selectedPlant()" #selPlantNgModel="ngModel" name="selPlantNgModel" required>
              <option disabled value="" selected>{{"Select Plant" | translate}}</option>
              <option *ngFor="let item of plants" value="{{item.PLANT}}">
                {{item.PLANT_NAME}} ({{item.PLANT}})</option>
            </select>
          </ion-col>
          <br>
          <ion-col size="4" style="text-align: left;">
            <ion-label>{{"Language" | translate}}</ion-label>
            <select class="responsive-select" style="border-radius: 5px;height: 35px;width:100%;margin-top: 5px;"
              [(ngModel)]="selLanguage" value="selLanguage" (change)="selectedLanguage()" #selLanguageNgModel="ngModel"
              name="selLanguageNgModel" required>
              <option disabled value="" selected>{{"Select Language" | translate}}</option>
              <option *ngFor="let item of languageList" value="{{item.LANGU}}">
                {{item.LANGU_TEXT}}</option>
            </select>
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  </div>

</ion-content>