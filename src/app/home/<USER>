import { Component, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { ResultType, UnviredCordovaSDK, UnviredResult } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertController } from '@ionic/angular';
import { DataService } from '../services/data.service';
import { TranslateService } from '@ngx-translate/core';
import { Platform } from '@ionic/angular';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
})
export class HomePage {
  public errorMessage: string = "";
  public languageList = [];
  public plants = [];
  public selPlant = "";
  public selLanguage = ""
  public isMasterdataLoaded: boolean = false;
  public userFullname = [];
  public incidentType = [];
  public checkIncidenType: boolean;
  public platform: string;

  constructor(
    public dataService: DataService,
    public alertController: AlertController,
    private ngZone: NgZone,
    public router: Router,
    public unviredSdk: UnviredCordovaSDK,
    private translate: TranslateService,
    private plt: Platform
  ) {

  }

  async ionViewDidEnter() {
    this.dataService.resetIdleWatcher();
    console.log("ionViewDidEnter called");
    let checkPlatform = this.plt.platforms();
    console.log("Platform information", checkPlatform);
    if (checkPlatform.includes("desktop")) {
      this.platform = "desktop"
    } else {
      this.platform = "nonDesktop"
    }
    console.log("current platform", this.platform);
    //  moment.locale(this.language);
    await this.pleaseWaitLoader();
    try {
      let isCredentialsRequriedResult: UnviredResult =
        await this.unviredSdk.isClientCredentialsSet();
      if (
        isCredentialsRequriedResult &&
        isCredentialsRequriedResult.data === false
      ) {
        let tempDetails = localStorage.getItem('clientDetails');
        if (tempDetails != null && tempDetails != null) {
          let inputdata = JSON.parse(localStorage.getItem('querydata'));
          let userDetails = JSON.parse(atob(tempDetails));
          await this.unviredSdk.setClientCredentials(userDetails)
          await this.dataService.getProfile(inputdata);
          this.dataService.setProfileCallIsDone(true);
          this.dataService.setFirstTimeFlag(false);
          await this.loadData();
        }
      }
      let checkUserId: UnviredResult =
        await this.unviredSdk.isClientCredentialsSet();
      if (checkUserId &&
        checkUserId.data === false) {
        this.dataService.logout();
      } else {
        await this.loadData();
        this.userLable();
      }
    } catch (error) {
      await this.dismissLoadingController();
      await this.dataService.displayAlert(error)
    }
    finally {
      // Ensure loader is dismissed in all cases
      await this.dismissLoadingController();
    }
  }
  async loadData() {
    console.log('loadData called')
    try {
      let profileCallIsDone = this.dataService.getProfileCallIsDone();
      if (!profileCallIsDone) {
        let inputdata = JSON.parse(localStorage.getItem('querydata'));
        await this.dataService.getProfile(inputdata);
        this.dataService.setProfileCallIsDone(true);
        this.dataService.setFirstTimeFlag(false);

      }
      profileCallIsDone = this.dataService.getProfileCallIsDone();
      let firstTimeFlag = this.dataService.getFirstTimeFlag()
      if (profileCallIsDone && !firstTimeFlag) {
        this.dataService.setFirstTimeFlag(true);
        let userDetailsData = await this.dataService.getData('USER_DETAILS_HEADER');
        if (userDetailsData.length > 0) {
          this.selPlant = userDetailsData[0].PLANT;
          this.selLanguage = userDetailsData[0].LANG;
          console.log("Default Plant: "+this.selPlant);
          console.log("Default Language: "+this.selLanguage);
          this.translate.use(this.selLanguage);
          localStorage.setItem('selLanguage', this.selLanguage);
          localStorage.setItem('selPlant', this.selPlant);

          await this.callMasterdata();

          let incidentTypeRes = await this.dataService.getData('INCIDENT_TYPES_HEADER', ' ORDER BY INCI_TYPE_DESC');
          if (incidentTypeRes.length > 0) {
            // If there is Data in incident type checkIncidenType is false
            this.checkIncidenType = false;
            this.incidentType = incidentTypeRes;
          } else {
            // If there is no Data in incident type checkIncidenType is true
            this.checkIncidenType = true;
            console.log("no incident types found");
          }
          console.log("checkIncidenType: "+this.checkIncidenType);

          let langHeaderRes = await this.dataService.getData('LANG_HEADER');
          if (langHeaderRes.length > 0) {
            this.languageList = langHeaderRes;
          } else {
            console.log("no languages found");
          }

          let plantHeaderRes = await this.dataService.getData('PLANT_HEADER');
          if (plantHeaderRes.length > 0) {
            this.plants = plantHeaderRes;
          } else {
            console.log("no plants found");
          }
        } else {
          console.log("no data found");
        }
      }
    } catch (error) {
      await this.dismissLoadingController();
      await this.dataService.displayAlert(error)
    }
    finally {
      // Ensure loader is dismissed in all cases
      await this.dismissLoadingController();
    }
  }

  async CreateSafetyIncident() {
    await this.callMasterdata();
    this.ngZone.run(() => this.router.navigate(['safety-incident']));

  }
  async CreateSafetyConversation() {
    await this.callMasterdata();
    this.ngZone.run(() => this.router.navigate(['safety-conversation']));
  }
  // CreateEnvironmentalIncident() {

  // }
  async NavToPropertyDamage() {
    await this.callMasterdata();
    this.ngZone.run(() => this.router.navigate(['property-damage']))
  }

  async CreateSafetyObservation() {
    await this.callMasterdata();
    this.ngZone.run(() => this.router.navigate(['safety-observation']));

  }

  async CreateEnvironmentIncident() {
    await this.callMasterdata();
    this.ngZone.run(() => this.router.navigate(['environment-incident']));
  }

  async Createincidentlogmonitor() {
    await this.callMasterdata();
    this.ngZone.run(() => this.router.navigate(['incident-log-monitor']));
  }
  // ReviewIncidents() {

  // }
  async callMasterdata() {
    localStorage.setItem('selLanguage', this.selLanguage);
    localStorage.setItem('selPlant', this.selPlant);

    if (!this.isMasterdataLoaded) {
      await this.pleaseWaitLoader();
      try {
        await this.dataService.getMasterData();
        this.isMasterdataLoaded = true;
      }
      finally {
        // Ensure loader is dismissed in all cases
        await this.dismissLoadingController();
      }
    }
  }

  logout() {
    this.confirmationDialog(this.translate.instant('Are you sure you want to logout?'));
  }
  async confirmationDialog(msg: string) {
    let inputdataOld = JSON.parse(localStorage.getItem('querydata'));
    const alert = await this.alertController.create({
      header: this.translate.instant('Confirmation'),
      mode: 'ios',
      message: `<strong>${msg}</strong>`,
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: this.translate.instant('Yes'),
          handler: async () => {
            this.dataService.logout();
            localStorage.setItem('querydata', JSON.stringify(inputdataOld));
          },
        },
      ],
    });
    await alert.present();
  }
  selectedLanguage() {
    let lan = localStorage.getItem('selLanguage');
    if (lan !== this.selLanguage && this.isMasterdataLoaded) {
      this.isMasterdataLoaded = false;
    }
    else {
      let selPlant = localStorage.getItem('selPlant');
      if (selPlant === this.selPlant) {
        this.isMasterdataLoaded = true;
      } else {
        this.isMasterdataLoaded = false;
      }
    }
    // localStorage.setItem('selLanguage', this.selLanguage);
    this.translate.use(this.selLanguage.toLowerCase());
  }
  selectedPlant() {
    let selPlant = localStorage.getItem('selPlant');
    if (selPlant !== this.selPlant && this.isMasterdataLoaded) {
      this.isMasterdataLoaded = false;
    }
    else {
      let lan = localStorage.getItem('selLanguage');
      if (lan === this.selLanguage) {
        this.isMasterdataLoaded = true;
      } else {
        this.isMasterdataLoaded = false;
      }
      localStorage.setItem('selPlant', this.selPlant);
    }
  }

  async userLable() {
    let user = await this.dataService.getData('USER_DETAILS_HEADER');
    if (user.length > 0) {
      this.userFullname = user;
    } else {
      console.log("no data found");
    }
  }

  async pleaseWaitLoader() {
    await this.dataService.pleaseWaitLoader();
  }
  async dismissLoadingController() {
    await this.dataService.dismissLoadingController();
  }
  async ionViewDidLeave() {
    console.log("ionViewDidLeave called")
    await this.dismissLoadingController();
  }
}