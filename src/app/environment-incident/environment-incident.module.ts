import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { EnvironmentIncidentPageRoutingModule } from './environment-incident-routing.module';

import { EnvironmentIncidentPage } from './environment-incident.page';
import { TranslateModule } from '@ngx-translate/core';
import { NgxDropzoneModule } from 'ngx-dropzone';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    EnvironmentIncidentPageRoutingModule,
    TranslateModule,
    NgxDropzoneModule
  ],
  declarations: [EnvironmentIncidentPage]
})
export class EnvironmentIncidentPageModule { }
