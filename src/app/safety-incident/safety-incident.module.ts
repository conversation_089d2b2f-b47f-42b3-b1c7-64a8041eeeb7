import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SafetyIncidentPageRoutingModule } from './safety-incident-routing.module';

import { SafetyIncidentPage } from './safety-incident.page';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SafetyIncidentPageRoutingModule,
    NgxDropzoneModule,
    TranslateModule
  ],
  declarations: [SafetyIncidentPage]
})
export class SafetyIncidentPageModule {}
