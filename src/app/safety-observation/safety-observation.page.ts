import { Component, ElementRef, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON><PERSON><PERSON><PERSON>roller, IonSelect, PopoverController } from '@ionic/angular';
import { format, parseISO, getDate, getMonth, getYear } from 'date-fns';
import { DataService } from '../services/data.service';
import * as moment from 'moment';
import { TranslateService } from '@ngx-translate/core';
import { INCIDENT_HEADER } from 'src/app/Constants/HEADER';
import { AttachmentService } from 'src/app/Services/attachment-service/attachment.service'
import { DOCUMENT_ATTACHMENT } from 'src/app/dataModels/DOCUMENT_ATTACHMENT';
import { Platform } from '@ionic/angular';

@Component({
  selector: 'app-safety-observation',
  templateUrl: './safety-observation.page.html',
  styleUrls: ['./safety-observation.page.scss'],
})
export class SafetyObservationPage implements OnInit {

  @ViewChild('mySelect') selectRef: IonSelect;

  public selIncidentType = "";
  public selworkareafIncident = "";
  public selAccidentLocationList = "";
  public selflocLocation = ""
  public searchEmpType = ""
  public empType = ""
  public selInjuredBodyPart = ""
  public selInjuryPart = ""
  public selTreatmentAction = ""
  public detailsOfIncident = ""
  public files: File[];
  public postMultimedias = [];
  public selDateTimeIncident: any;
  public workareafIncident = [];
  public incidentType = [];
  public injuryList = [];
  public flocLocationList = [];
  public injuredBodyPartList = [];
  public accidentLocationList = [];
  public treatmentActionList = [];
  public errorMessage: string = "";
  public selempType: string = "";
  public dateValue = "";
  public selEmpNo: string;
  public isHideBodyInjury: boolean = false;
  public isClicked: boolean = false;
  public isMobileView: boolean = false;
  public maxValue;
  public searchFname = "";
  public searchLname = "";
  public searchEmployeeList = [];
  public showEmpErr: boolean = false;
  public isCancelDateTime: boolean = false;
  public empValue = "";
  public tempValue = "";
  public contractorValue = ""
  public selLang = ""
  public customAlertOptions = {}
  public totalFileSize = 0;
  public customText: boolean = false;
  public cause = [];
  public selectedCause = "";
  public causeFields = new INCIDENT_HEADER();
  public INPUT_SYSID: any;
  public SYSID = "";
  public newAttachment;
  public allAttachment = [];
  public userFullname = [];
  public fileURL: any[] = [];
  public platform: string;
  public significantActivity = "";

  constructor(
    public dataService: DataService,
    private ngZone: NgZone,
    public router: Router,
    public alertController: AlertController,
    public popCtrl: PopoverController,
    private translate: TranslateService,
    private attachmentService: AttachmentService,
    private plt: Platform

  ) {
    this.files = [];
    this.maxValue = moment().format();
  }

  async ngOnInit() {
    console.log("ngOnInit called");
    // console.log("Current platform", this.plt.platforms());
    // let checkPlatform = this.plt.platforms();
    // this.platform = checkPlatform[1];
    // console.log("checkPlatform", checkPlatform);
    this.platform = await this.dataService.checkPlatform();
    console.log("current platform", this.platform);
    let selectedPlant = localStorage.getItem('selPlant');
    console.log("selectedPlant", selectedPlant);
  }

  async ionViewWillEnter() {
    console.log("ionViewWillEnter called")
    this.userLable();
    this.empType = this.translate.instant("Employee");
    this.empValue = this.translate.instant("Employee");
    // this.tempValue = this.translate.instant("TEMP");
    // this.contractorValue = this.translate.instant("CONTRACTOR");

    this.tempValue = "TEMP";
    this.contractorValue = "CONTRACTOR";
    
    this.selLang = localStorage.getItem('selLanguage');
    this.selDateTimeIncident = moment(new Date().toISOString()).locale(this.selLang).format();


    this.customAlertOptions = {
      header: this.translate.instant('Click to select an Employee'),
      translucent: true,
    };

    this.resizeScreen();
    this.ngZone.run(() => {
      window.onresize = () => {
        this.resizeScreen();
      };
    });
    await this.loadData();
  }
  resizeScreen() {
    if (window.innerWidth < 650) {
      this.isMobileView = true;
    } else {
      this.isMobileView = false;
    }
  }
  async loadData() {
    try {
      await this.pleaseWaitLoader();

      let incidentTypeRes = await this.dataService.getData('INCIDENT_TYPES_HEADER');
      if (incidentTypeRes.length > 0) {
        this.incidentType = incidentTypeRes;
      } else {
        console.log("no incident types found");
      }

      let injuryListRes = await this.dataService.getData('INJURIES_HEADER');
      if (injuryListRes.length > 0) {
        this.injuryList = injuryListRes;
      } else {
        console.log("no injury list found");
      }

      let injuredBodyPartRes = await this.dataService.getData('BODY_PARTS_HEADER');
      if (injuredBodyPartRes.length > 0) {
        this.injuredBodyPartList = injuredBodyPartRes;
      } else {
        console.log("no function location found");
      }

      let causes = await this.dataService.getData('CAUSE_DESC_HEADER', " WHERE INCI_TYPE = 'OTH' ORDER BY CAUSE_DESC");
      if (causes.length > 0) {
        this.cause = causes;
        console.log("cause", this.cause);
      } else {
        console.log("no cause list found");
      }

      // for treatment action 
      let treatmentActionRes = await this.dataService.getData('IMMI_RESPONSE_HEADER');
      if (treatmentActionRes.length > 0) {
        this.treatmentActionList = treatmentActionRes;
      }

      let workareaRes = await this.dataService.getData('WORKAREAS_HEADER', ' ORDER BY WORKAREA_DESC');
      if (workareaRes.length > 0) {
        this.workareafIncident = workareaRes;
      } else {
        console.log("no workarea found");
      }
    }
    finally {
      await this.dismissLoadingController();
    }

  }

  searchByEmp(ev) {
    this.searchFname = "";
    this.searchLname = "";

  }
  searchByFname() {
    // this.searchFname = this.searchFname.toUpperCase();
    this.searchEmpType = ""
    this.searchLname = "";
  }
  searchByLname() {
    // this.searchLname = this.searchLname.toUpperCase();
    this.searchEmpType = ""
    this.searchFname = "";
  }

  formatDate(value: string) {
    if (value != "") {
      this.isCancelDateTime = false;
      let date = format(parseISO(value), 'MMM dd, yyyy h:mma');
      return date;
    } else {
      this.isCancelDateTime = true;
    }
  }

  onCancelDateTime() {
    if (this.dateValue === "") {
      this.isCancelDateTime = true;
    } else {
      this.isCancelDateTime = false;
    }
  }
  async setCompletion(event: any) {
    this.selempType = this.empType;
    this.searchEmpType = "";
    this.searchLname = "";
    this.searchFname = "";
    if (this.selempType === this.translate.instant('Employee')) {
      this.showEmpErr = true;
    } else {
      this.showEmpErr = false;
    }
  }

  selecteIncidentType() {
    this.selInjuredBodyPart = "";
    this.selInjuryPart = "";
    this.selTreatmentAction = "";
    for (let i = 0; i < this.incidentType.length; i++) {
      if (this.incidentType[i].INCI_TYPE === this.selIncidentType) {
        this.isHideBodyInjury = (this.incidentType[i].HIDE_BODY_INJURY === 'X') ? true : false;
      }
    }
  }

  async selectedWorkArea() {
    this.selAccidentLocationList = "";
    this.selflocLocation = "";
    let flocLocationRes = await this.dataService.getData('FUNC_LOC_HEADER', `WORKAREA = '${this.selworkareafIncident}'`);
    if (flocLocationRes.length > 0) {
      this.flocLocationList = flocLocationRes;
    } else {
      this.flocLocationList = [];
      console.log("no function location found");
    }

    let accidentLocationRes = await this.dataService.getData('ACC_LOCS_HEADER', `WORKAREA = '${this.selworkareafIncident}'`);
    if (accidentLocationRes.length > 0) {
      this.accidentLocationList = accidentLocationRes;
    } else {
      this.accidentLocationList = [];
      console.log("no accident location found");
    }
  }
  async searchEmployee() {
    if (this.empType === this.translate.instant('Employee')) {
      try {
        await this.pleaseWaitLoader();
        // this.selempType = "";
        let empRes = await this.dataService.getEmployee(this.searchEmpType, this.searchFname.toUpperCase(), this.searchLname.toUpperCase());
        // empRes = JSON.parse(empRes);
        if (empRes && empRes.EMPLOYEE && empRes.EMPLOYEE.length > 0 && empRes.EMPLOYEE[0] && empRes.EMPLOYEE[0].EMPLOYEE_HEADER) {
          await this.dismissLoadingController();
          this.searchEmployeeList = [];
          if (empRes.EMPLOYEE.length === 1) {
            let header = empRes.EMPLOYEE[0].EMPLOYEE_HEADER;
            this.selempType = header.FNAME + ' ' + header.NACHN + ' (' + header.EMPLOYEE + ')'
            this.selEmpNo = header.EMPLOYEE;
          } else {
            for (let i = 0; i < empRes.EMPLOYEE.length; i++) {
              this.searchEmployeeList.push(empRes.EMPLOYEE[i].EMPLOYEE_HEADER);
            }
            if (this.searchEmployeeList.length > 0 && this.selectRef) {
              this.ngZone.run(async () => {
                await this.selectRef.open();
              });
            }
          }

        } else {
          await this.dismissLoadingController();
          this.errorMessage = await this.dataService.handleInfoMessage1(empRes);
          if (this.errorMessage.length === 0) {
            this.errorMessage = this.translate.instant('No Employee Details Found.')
          }
          // await this.dataService.displayAlert(this.translate.instant('No Employee Details Found.'));
        }
        this.showEmpErr = true;
      }
      finally {
        await this.dismissLoadingController();
      }
    } else {
      this.showEmpErr = false
    }

  }
  setEmployee(ev) {
    console.log("" + this.selempType)
    if (this.empType === this.translate.instant('Employee')) {
      for (let i = 0; i < this.searchEmployeeList.length; i++) {
        if (this.searchEmployeeList[i].EMPLOYEE === this.selEmpNo) {
          this.selempType = this.searchEmployeeList[i].FNAME + ' ' + this.searchEmployeeList[i].NACHN + ' (' + this.searchEmployeeList[i].EMPLOYEE + ')';
        }
      }
    }

  }

  previewFile(event: any) {
    if (event.target.files[0]) {
      this.files.push(event.target.files[0]);
    }
    // const fileType = this.files.type;
    // const mimeType = fileType ? fileType.split('/')[1] : '';

    let reader = new FileReader();
    if (event.target.files[0]) {
      reader.readAsDataURL(event.target.files[0]);
    }
    reader.onload = (_event) => {
      this.fileURL.push(<string>reader.result)
      console.log("mobile file preview data", this.fileURL);
    }
  }

  deleteImage(index: number) {
    this.fileURL.splice(index, 1);
    this.files.splice(index, 1);
  }

  // Adding Attachments 
  async onSelect(event) {
    if (event.addedFiles.type === "image/jpeg") {
    }
    this.files.push(...event.addedFiles);
  }

  // Upload file to server
  async saveFile() {
    for (var i = 0; i < this.files.length; i++) {
      await this.attachmentService.uploadFileToServer(this.files[i]).then(async (result) => {
        let documetAttachmentHeader = this.createDocumentAttachmentHeader(result.attachmentId, this.files[i]);
        this.allAttachment.push(documetAttachmentHeader);
      }, (error) => {
        this.dismissLoadingController();
        this.dataService.displayAlert(this.translate.instant("Attachment upload failed, Please try again"));
      })
    }
  }

  createDocumentAttachmentHeader(attachmentUid: string, file: any): any {
    this.newAttachment = <DOCUMENT_ATTACHMENT>{};
    this.newAttachment.UID = attachmentUid;
    this.newAttachment.FILE_NAME = file.name;
    this.newAttachment.MIME_TYPE = file.type;
    this.newAttachment.ATTACHMENT_STATUS = "UPLOADED";
    return this.newAttachment;
  }

  onRemove(event) {
    let position = this.files.indexOf(event);
    // var removedFile = this.files[position].size;
    this.files.splice(position, 1);
    // this.totalFileSize -= (removedFile / 1024 / 1024);
  }

  cancel() {
    if (this.selIncidentType !== "" || this.selworkareafIncident !== "" || this.selAccidentLocationList !== "" || this.selflocLocation !== "" || this.searchEmpType !== "" || this.empType !== "" || this.selInjuredBodyPart !== "" || this.selInjuryPart !== "" || this.selTreatmentAction !== "" || this.detailsOfIncident !== "") {
      this.confirmationCancelDialog(this.translate.instant('Unsaved changes will be lost. Continue?'));
    } else {
      this.ngZone.run(() => this.router.navigate(['home']));
    }
  }

  async confirmationCancelDialog(msg: string) {
    const alert = await this.alertController.create({
      header: this.translate.instant('Warning'),
      mode: 'ios',
      cssClass: 'secondary',
      message: `<strong>${msg}</strong>`,
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => { },
        },
        {
          text: this.translate.instant('Yes'),
          cssClass: 'secondary',
          handler: async () => {
            this.ngZone.run(() => this.router.navigate(['home']));
          },
        },
      ],
    });
    await alert.present();
  }

  async createSafetyIncident() {
    try {
      await this.pleaseWaitLoader();
      this.INPUT_SYSID = JSON.parse(localStorage.getItem('querydata'));
      console.log("INPUT_SYSID", this.INPUT_SYSID.SYSID);
      this.SYSID = this.INPUT_SYSID.SYSID;
      console.log("SYSID", this.SYSID);
      await this.saveFile();
      let userDetailsData = await this.dataService.getData('USER_DETAILS_HEADER');
      let selectedPlant = localStorage.getItem('selPlant');
      if (userDetailsData.length > 0) {
        let data = {
          INCIDENT: [
            {
              INCIDENT_HEADER: {
                INCIDENT_NO: String(Math.floor(100000 + Math.random() * 900000)),
                PLANT: selectedPlant,
                INCIDENT_TYPE: "NMS",
                INCIDENT_DATE: moment(new Date(this.selDateTimeIncident)).format('YYYY-MM-DD'),
                INCIDENT_TIME: moment(new Date(this.selDateTimeIncident)).format('HH:mm:ss'),
                EMP_NO: (this.empType && this.empType === this.translate.instant('Employee')) ? this.selEmpNo : this.selempType,
                // EMP_NO: (this.empType && this.empType === 'Employee' ) ? this.selEmpNo : 'TEMP',
                WORKAREA: this.selworkareafIncident,
                AC_LOC: this.selAccidentLocationList,
                FUNC_LOC: this.selflocLocation,
                BODYPART: this.selInjuredBodyPart,
                INJURY: this.selInjuryPart,
                TYPE_OF_FIRSTAID: this.selTreatmentAction,
                SHIFT_INFO: "",
                AC_CATEGORY: "",
                DETAIL_DESC: this.detailsOfIncident,
                CLOSE_OUT: this.causeFields.CLOSE_OUT,
                CAUSE: this.selectedCause,
                CAUSE_DESC: this.causeFields.CAUSE_DESC,
                SYSID: this.SYSID,
                SIGN_ACTIVITY: this.significantActivity,
              },
              INCIDENT_ATTACHMENT: this.allAttachment
            }
          ]
        }
        let createSafetyIncidentRes = await this.dataService.createSafetyIncident(data);
        if (createSafetyIncidentRes.type == ResultType.success) {
          await this.dismissLoadingController();
          if (createSafetyIncidentRes.data &&
            createSafetyIncidentRes.data.INCIDENT.length > 0 &&
            createSafetyIncidentRes.data.INCIDENT[0]
              .INCIDENT_HEADER) {
            await this.displaySuccessAlert(this.translate.instant("Near Miss") + "/" + this.translate.instant("Observation") + " " + createSafetyIncidentRes.data.INCIDENT[0].INCIDENT_HEADER.INCIDENT_NO + " " + this.translate.instant("created successfully in SAP."));
          } else {
            console.log("no workarea found");
          }
          this.ngZone.run(() => this.router.navigate(['home']));
        } else {
          await this.dismissLoadingController();
          if (createSafetyIncidentRes.message) {
            this.errorMessage = createSafetyIncidentRes.message;
          }
          if (createSafetyIncidentRes.InfoMessage) {
            this.errorMessage = this.dataService.handleInfoMessage(createSafetyIncidentRes);
          }
          await this.dataService.displayAlert(this.errorMessage);
        }
      } else {
        console.log("no user details found")
      }
    }
    finally {
      await this.dismissLoadingController();
    }
  }

  goBackToLastLocation() {
    this.ngZone.run(() => this.router.navigate(['home']));
  }

  async userLable() {
    let user = await this.dataService.getData('USER_DETAILS_HEADER');
    if (user.length > 0) {
      this.userFullname = user;
    } else {
      console.log("no data found");
    }
  }
  async pleaseWaitLoader() {
    await this.dataService.pleaseWaitLoader();
  }

  async dismissLoadingController() {
    await this.dataService.dismissLoadingController();
  }

  async displaySuccessAlert(msg: string) {
    const alert = await this.alertController.create({
      header: this.translate.instant('Info'),
      message: msg,
      buttons: [{ text: 'Ok' }],
    });
    await alert.present();
  }

}
