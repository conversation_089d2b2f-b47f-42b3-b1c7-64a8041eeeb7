<ion-header>
  <ion-toolbar color="lightBlue">
    <ion-buttons slot="start">
      <ion-button (click)='goBackToLastLocation()'>
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <div class="header-title">
      <ion-title>
        <span class="responsive-title" style="vertical-align: -webkit-baseline-middle !important;">{{"Near Miss" |
          translate}} / {{"Observation" | translate}}</span>
      </ion-title>
      <ion-title class="text-center login">
        <span *ngFor="let users of userFullname" class="user-title">{{users.FULLNAME |
          titlecase }} - Logged In</span>
      </ion-title>
      <ion-title>
        <img src="assets/img/logo.png" class="mobile-responsive-logo" style="width: 125px; float: right;">
      </ion-title>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content>
  <form #Form="ngForm">
    <ion-grid style="margin-bottom: -10px !important;padding:15px;margin-left:10px;color: black !important;">

      <!-- Date and Time of Incident -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Date and Time of Incident" | translate}}<span class="text-danger">*</span>
          </ion-label>
          <div style="border: 1px solid black;height: 35px;border-radius: 5px;" button="true" id="open-date-input">
            <div style="padding: 8px;">
              {{dateValue}}
              <span style="float: right;">
                <ion-icon name="calendar-clear-outline" button="true" id="open-date-input"></ion-icon>
              </span>
            </div>

            <ion-popover trigger="open-date-input" show-backdrop="false">
              <ng-template>
                <ion-datetime #popoverDatetime show-default-buttons="true" max="{{maxValue}}"
                  (ionChange)="dateValue = formatDate(popoverDatetime.value);" (ionCancel)="onCancelDateTime()"
                  [(ngModel)]="selDateTimeIncident" #selDateTimeIncidentNgModel="ngModel"
                  name="selDateTimeIncidentNgModel" locale={{selLang}}>
                </ion-datetime>
              </ng-template>
            </ion-popover>

            <!-- <div *ngIf="selDateTimeIncidentNgModel.errors && (selDateTimeIncidentNgModel.dirty || selDateTimeIncidentNgModel.touched)">
            <small *ngIf="selDateTimeIncidentNgModel.errors.required" class="text-danger">Date and Time of Incident is required.</small>
          </div> -->
          </div>
          <div *ngIf="(selDateTimeIncident === '' && isCancelDateTime)">
            <small class="text-danger">{{"Date and Time of Incident is required." | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Select Work Area of the Incident -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Work Area of the Incident" | translate}}<span class="text-danger">*</span>
          </ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selworkareafIncident"
            value="selworkareafIncident" (change)="selectedWorkArea()" #selworkareafIncidentNgModel="ngModel"
            name="selworkareafIncidentNgModel" required>
            <option disabled value="" selected>{{"Select Work Area of the Incident" | translate}}</option>
            <option *ngFor="let item of workareafIncident" value="{{item.WORKAREA}}">
              {{item.WORKAREA_DESC}}</option>
          </select>
          <div
            *ngIf="selworkareafIncidentNgModel.errors && (selworkareafIncidentNgModel.dirty || selworkareafIncidentNgModel.touched)">
            <small *ngIf="selworkareafIncidentNgModel.errors.required" class="text-danger">{{"Work Area of the Incident
              is required." | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Select the Functional Location -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Functional Location" | translate}}</ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selflocLocation" required
            value="selflocLocation" #selflocLocationNgModel="ngModel" name="selflocLocationNgModel">
            <option disabled value="" selected>{{"Select the Functional Location" | translate}}</option>
            <option *ngFor="let item of flocLocationList" value="{{item.FUNC_LOC}}">
              {{item.FUNC_LOC_DESC}}</option>
          </select>
        </ion-col>
      </ion-row>

      <!-- Select the Accident Location -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Accident Location" | translate}}<span
              class="text-danger">*</span></ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selAccidentLocationList" required
            value="selAccidentLocationList" #selAccidentLocationListNgModel="ngModel"
            name="selAccidentLocationListNgModel">
            <option disabled value="" selected>{{"Select the Accident Location" | translate}}</option>
            <option *ngFor="let item of accidentLocationList" value="{{item.ACC_LOC}}">
              {{item.ACC_LOC_DESC}}</option>
          </select>
          <div
            *ngIf="selAccidentLocationListNgModel.errors && (selAccidentLocationListNgModel.dirty || selAccidentLocationListNgModel.touched)">
            <small *ngIf="selAccidentLocationListNgModel.errors.required" class="text-danger">{{"Accident Location is
              required" | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Injured Person Employee Number -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-radio-group [(ngModel)]="empType" (ionChange)="setCompletion($event)" style="display: inline-flex;"
            #empTypeNgModel="ngModel" name="empTypeNgModel">
            <ion-radio mode="md" slot="start" color="primary" value={{empValue}}></ion-radio>&nbsp;&nbsp;
            <ion-label>{{"Employee" | translate}}</ion-label>&nbsp;&nbsp;&nbsp;&nbsp;

            <ion-radio mode="md" slot="start" color="primary" value={{tempValue}}></ion-radio>&nbsp;&nbsp;
            <ion-label>{{"Temp" | translate}}</ion-label>&nbsp;&nbsp;&nbsp;&nbsp;

            <ion-radio mode="md" slot="start" color="primary" value={{contractorValue}}></ion-radio>&nbsp;&nbsp;
            <ion-label>{{"Contractor" | translate}}</ion-label><span class="text-danger">*</span>

          </ion-radio-group>
          <ion-input style="border: 1px solid black;border-radius: 5px;" [(ngModel)]="selempType" readonly="true"
            #selempTypeNgModel="ngModel" name="selempTypeNgModel">
          </ion-input>
        </ion-col>
      </ion-row>


      <!-- Injured body part -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Potential Injured Body Part" | translate}}</ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selInjuredBodyPart" required
            value="selInjuredBodyPart" #selInjuredBodyPartNgModel="ngModel" name="selInjuredBodyPartNgModel">
            <option disabled value="" selected>{{"Potential Injured Body Part" | translate}}</option>
            <option *ngFor="let item of injuredBodyPartList" value="{{item.BODYPART_CODE}}">
              {{item.BODYPART_DESC}}</option>
          </select>
        </ion-col>
      </ion-row>

      <!-- Type of Injury -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Potential Type of Injury" | translate}}</ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selInjuryPart" required
            value="selInjuryPart" #selInjuryPartNgModel="ngModel" name="selInjuryPartNgModel">
            <option disabled value="" selected>{{"Potential Type of Injury" | translate}}</option>
            <option *ngFor="let item of injuryList" value="{{item.INJURY_CODE}}">
              {{item.INJURY_DESC}}</option>
          </select>
        </ion-col>
      </ion-row>

      <!-- Select the type of treatment/Immediate action -->
      <!-- <ion-row >
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Type of Treatment/Immediate Action" | translate}}</ion-label>
          <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selTreatmentAction" required
            value="selTreatmentAction" #selTreatmentActionNgModel="ngModel" name="selTreatmentActionNgModel">
            <option disabled value="" selected>{{"Select the Type of Treatment/Immediate Action" | translate}}</option>
            <option *ngFor="let item of treatmentActionList" value="{{item.IMMI_RES_CODE}}">
              {{item.IMMI_RES_DESC}}</option>
          </select>
        </ion-col>
      </ion-row> -->

      <!-- Is this safety item completed? -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <p class="p-tag-header m-top ion-no-margin">{{"Is this safety item completed" | translate}}<span
              class="text-danger">*</span>
          </p>
          <ion-radio-group [(ngModel)]="causeFields.CLOSE_OUT" [ngModelOptions]="{standalone: true}" required>
            <ion-radio mode="md" value="X"></ion-radio>
            &nbsp;&nbsp;
            <ion-label for="yes" style="vertical-align: text-bottom;">{{"Yes" | translate}}</ion-label>
            &nbsp;&nbsp;
            <ion-radio mode="md" value=""></ion-radio> &nbsp;&nbsp;
            <ion-label for="no" style="vertical-align: text-bottom;">{{"No" | translate}}</ion-label>
          </ion-radio-group>
          <div *ngIf="(causeFields.CLOSE_OUT == 'unchecked')">
            <small class="text-danger">{{"Safety item completed is required" | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <div class="row" *ngIf="causeFields.CLOSE_OUT == 'X'">
        <ion-row>
          <ion-col [attr.size]=" isMobileView ? 12 : 5">
            <ion-label class="p-tag-header">{{"Cause" | translate}}<span class="text-danger">*</span>
            </ion-label>
            <select style="border-radius: 5px;height: 35px;width:100%" [(ngModel)]="selectedCause" value="selectedCause"
              #selectedCauseNgModel="ngModel" name="selectedCauseNgModel" required>
              <option disabled value="" selected>{{"Select Cause" | translate}}</option>
              <option *ngFor="let item of cause" value="{{item.CAUSE}}">
                {{item.CAUSE_DESC}}</option>
            </select>
            <div *ngIf="selectedCauseNgModel.errors && (selectedCauseNgModel.dirty || selectedCauseNgModel.touched)">
              <small *ngIf="selectedCauseNgModel.errors.required" class="text-danger">{{"Cause
                is required." | translate}}</small>
            </div>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col [attr.size]="isMobileView ? 12 : 5">
            <ion-label class="p-tag-header">{{"Corrective/ Preventative Action Taken" | translate}}<span
                class="text-danger">*</span>
            </ion-label>
            <ion-textarea wrap="soft" rows="3" placeholder="{{ 'Corrective/ Preventative Action Taken' | translate }}"
              [(ngModel)]="causeFields.CAUSE_DESC" style="border: 1px solid black;border-radius: 5px;margin-top: 0px;"
              #detailsOfCauseNgModel="ngModel" name="detailsOfCauseNgModel" maxlength="120" required="true">
            </ion-textarea>
            <div *ngIf="detailsOfCauseNgModel.errors && (detailsOfCauseNgModel.dirty || detailsOfCauseNgModel.touched)">
              <small *ngIf="detailsOfCauseNgModel.errors.required" class="text-danger">{{"Cause Description is
                required." | translate}}</small>
            </div>
          </ion-col>
        </ion-row>
      </div>

      <!-- Short Description -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Short Description" | translate}}<span class="text-danger">*</span>
          </ion-label>
          <ion-input style="border: 1px solid black;border-radius: 5px;" [(ngModel)]="significantActivity"
            placeholder="{{ 'Short Description' | translate }}" #significantActivityNgModel="ngModel"
            name="significantActivityNgModel" maxLength="60" required="true">
          </ion-input>
          <div
            *ngIf="significantActivityNgModel.errors && (significantActivityNgModel.dirty || significantActivityNgModel.touched)">
            <small *ngIf="significantActivityNgModel.errors.required" class="text-danger">{{"Significant Activity is
              required." | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Details of the incident -->
      <ion-row>
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <ion-label class="p-tag-header">{{"Details of the Observation" | translate}}
            <span class="text-danger">*</span>
          </ion-label>
          <ion-textarea wrap="soft" rows="3"
            placeholder="{{'Details of the Observation' | translate}}"
            [(ngModel)]="detailsOfIncident" style="border: 1px solid black;border-radius: 5px;margin-top: 0px;"
            #detailsOfIncidentNgModel="ngModel" name="detailsOfIncidentNgModel" required="true">
          </ion-textarea>
          <div
            *ngIf="detailsOfIncidentNgModel.errors && (detailsOfIncidentNgModel.dirty || detailsOfIncidentNgModel.touched)">
            <small *ngIf="detailsOfIncidentNgModel.errors.required" class="text-danger">{{"Details of the Incident is
              required." | translate}}</small>
          </div>
        </ion-col>
      </ion-row>

      <!-- Attachments -->
      <ion-row *ngIf="platform == 'desktop'">
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <div>
            <ion-label class="p-tag-header">{{"Attachments" | translate}}</ion-label>
            <ngx-dropzone (change)="onSelect($event)">
              <ngx-dropzone-label>
                <p>{{"Drop files here" | translate}}</p>
                <p>{{"Or" | translate}}</p>
                <p>{{"Click to browse and add" | translate}}</p>
              </ngx-dropzone-label>
              <ngx-dropzone-preview ngProjectAs="ngx-dropzone-preview" *ngFor="let f of files" [file]="f"
                [removable]="true" (removed)="onRemove(f)">
                <ngx-dropzone-label style="width: 100%;">{{ f.name }}</ngx-dropzone-label>
              </ngx-dropzone-preview>
            </ngx-dropzone>
            <div id="AttachmentError"></div>
          </div>
        </ion-col>
      </ion-row>

      <!-- Attachments for mobile view-->
      <ion-row *ngIf="platform != 'desktop'">
        <ion-col [attr.size]="isMobileView ? 12 : 5">
          <label for="files" class="btn p-tag-header">{{"Upload Attachments" | translate}}</label><br>
          <input type="file" id="fileInput" class="custom-file-input" accept="image/*" (change)="previewFile($event)"
            style="color: transparent;" capture><br>
          <label for="fileInput" class="custom-file-button">{{"Camera" | translate}}</label>

          <div class="image-container-one" *ngIf="files.length > 0">
            <div class="image-flex">
              <div id="uploadedImage" class="image-container" *ngFor="let url of fileURL; let i = index">
                <img src="{{url}}" height="200" alt="Image preview...">
                <button class="remove-button" (click)="deleteImage(i)">{{"Remove" | translate}}</button>
              </div>
            </div>
          </div>

          <div class="image-container-two" *ngIf="files.length <= 0">
            <div>
              <p class="p-tag-header">{{"No images has been captured" | translate}}</p>
            </div>
          </div>

        </ion-col>
      </ion-row>
    </ion-grid>


  </form>
</ion-content>


<ion-footer>
  <ion-toolbar class="toolbar-bg">
    <ion-button slot="end" (click)="createSafetyIncident()" class="footer-btn"
      [disabled]="(selDateTimeIncident === '') || (causeFields.CLOSE_OUT == 'unchecked') || (selworkareafIncidentNgModel.errors || selworkareafIncidentNgModel.invalid) || (significantActivityNgModel.errors || significantActivityNgModel.invalid) || (selAccidentLocationListNgModel.errors || selAccidentLocationListNgModel.invalid) || (detailsOfIncidentNgModel.errors || detailsOfIncidentNgModel.invalid) || (selempType === empValue) || (causeFields.CLOSE_OUT == 'X' && ((!selectedCause) || (!causeFields.CAUSE_DESC)))">
      {{"Submit" | translate}}
    </ion-button>
    <ion-button slot="end" (click)="cancel()" class="footer-btn" style="margin-right: 20px !important;">{{"Cancel" |
      translate}}
    </ion-button>
  </ion-toolbar>
</ion-footer>