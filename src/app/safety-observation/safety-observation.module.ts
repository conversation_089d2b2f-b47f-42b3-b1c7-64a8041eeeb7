import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SafetyObservationPageRoutingModule } from './safety-observation-routing.module';

import { SafetyObservationPage } from './safety-observation.page';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    NgxDropzoneModule,
    SafetyObservationPageRoutingModule,
    TranslateModule
  ],
  declarations: [SafetyObservationPage]
})
export class SafetyObservationPageModule {}
