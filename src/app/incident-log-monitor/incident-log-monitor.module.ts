import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { IncidentLogMonitorPageRoutingModule } from './incident-log-monitor-routing.module';

import { IncidentLogMonitorPage } from './incident-log-monitor.page';
import { TranslateModule } from '@ngx-translate/core';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ChipModule } from 'primeng/chip';
import { ButtonModule } from 'primeng/button';
import { RadioButtonModule } from 'primeng/radiobutton';
import { BadgeModule } from 'primeng/badge';
import { DropdownModule } from 'primeng/dropdown';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    IncidentLogMonitorPageRoutingModule,
    TranslateModule,
    NgxDropzoneModule,
    TableModule,
    InputTextModule,
    ChipModule,
    ButtonModule,
    RadioButtonModule,
    BadgeModule,
    DropdownModule
  ],
  declarations: [IncidentLogMonitorPage]
})
export class IncidentLogMonitorPageModule { }
