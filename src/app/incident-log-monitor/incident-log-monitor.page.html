<ion-header>
  <ion-toolbar color="lightBlue">
    <ion-buttons slot="start">
      <ion-button (click)='goBackToLastLocation()'>
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <div class="header-title">
      <ion-title>
        <span class="responsive-title" style="vertical-align: -webkit-baseline-middle !important;">{{"Incident Log
          Monitor" | translate}}</span>
      </ion-title>
      <ion-title class="text-center login">
        <span *ngFor="let users of userFullname" class="user-title">{{users.FULLNAME |
          titlecase }} - Logged In</span>
      </ion-title>
      <ion-title>
        <img src="assets/img/logo.png" class="mobile-responsive-logo" style="width: 125px; float: right;">
      </ion-title>
    </div>
  </ion-toolbar>
  <!-- <ion-toolbar>
    <ion-searchbar class="log-search" style="--box-shadow: none" (ionChange)="handleChange($event)">
    </ion-searchbar>
  </ion-toolbar> -->
</ion-header>

<ion-content>

  <!-- PrimeNg Table for Desktop view -->
  <div *ngIf="platform == 'desktop'">
    <p-table #taskTable [value]="filteredIncidentData" [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
      responsiveLayout="scroll" [paginatorPosition]="'top'" [scrollHeight]="'100%'"
      [rowsPerPageOptions]="[5, 10, 20, 50]"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" [globalFilterFields]="['MONITOR_HEADER.INCIDENT_NO','MONITOR_HEADER.INCIDENT_TYPE','MONITOR_HEADER.INCIDENT_DATE','MONITOR_HEADER.WORKAREA',
    'MONITOR_HEADER.REPORTED_BY','MONITOR_HEADER.DETAIL_DESC','MONITOR_HEADER.EXECUTION_DATE']">

      <ng-template pTemplate="caption">
        <button pButton label="{{'Clear' | translate}}" class="p-button-outlined" icon="pi pi-filter-slash"
          (click)="clear(taskTable)"></button>

        <span class="p-input-icon-left search-float-right">
          <i class="pi pi-search"></i>
          <input pInputText type="text" (input)="taskTable.filterGlobal($any($event.target).value, 'contains')"
            placeholder="{{'Search' | translate}}" />
        </span>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="MONITOR_HEADER.INCIDENT_NO">{{"I/A no" | translate}}<p-sortIcon
              field="MONITOR_HEADER.INCIDENT_NO"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.INCIDENT_TYPE">{{"Incident Type" | translate}}<p-sortIcon
              field="MONITOR_HEADER.INCIDENT_TYPE"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.INCIDENT_DATE">{{"Event Date" | translate}}<p-sortIcon
              field="MONITOR_HEADER.INCIDENT_DATE"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.WORKAREA">{{"Workarea" | translate}}<p-sortIcon
              field="MONITOR_HEADER.WORKAREA"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.WORKAREA">{{"Accident Location" | translate}}<p-sortIcon
              field="MONITOR_HEADER.WORKAREA"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.REPORTED_BY">{{"Reported By" | translate}}<p-sortIcon
              field="MONITOR_HEADER.REPORTED_BY"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.DETAIL_DESC">{{"Short Description" | translate}}<p-sortIcon
              field="MONITOR_HEADER.DETAIL_DESC"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.EXECUTION_DATE">{{"Execution Date" | translate}}<p-sortIcon
              field="MONITOR_HEADER.EXECUTION_DATE"></p-sortIcon>
          </th>
          <th>{{"Update" | translate}}</th>
        </tr>
        <tr>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.INCIDENT_NO" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.INCIDENT_TYPE" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.INCIDENT_DATE" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.WORKAREA" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.AC_LOC_DESC" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.REPORTED_BY" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.DETAIL_DESC" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.EXECUTION_DATE" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th></th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-task>
        <tr>
          <td>{{task.MONITOR_HEADER.INCIDENT_NO}}
            <span *ngIf="task.ATTACHMENTS != null && task.ATTACHMENTS.length > 0" (click)="attachmentIocnClicked(task)" class="pi attachment-icon pi-paperclip"></span>
          </td>
          <td>{{task.MONITOR_HEADER.INCIDENT_TYPE}}</td>
          <td>{{task.MONITOR_HEADER.INCIDENT_DATE | date: 'd MMM, yyyy' }}</td>
          <td>{{task.MONITOR_HEADER.WORKAREA}}</td>
          <td>{{task.MONITOR_HEADER.AC_LOC_DESC}}</td>
          <td>{{task.MONITOR_HEADER.REPORTED_BY}}</td>
          <td>{{task.MONITOR_HEADER.DETAIL_DESC}}</td>
          <td>{{task.MONITOR_HEADER.EXECUTION_DATE | date: 'd MMM, yyyy'}}</td>
          <td>
            <ion-buttons>
              <ion-button disabled="true" *ngIf="task.MONITOR_HEADER.INCIDENT_TYPE == 'ZON'"
                (click)="updateIncident(task.MONITOR_HEADER)">
                <ion-icon name="create-outline" style="font-size: 30px;">
                </ion-icon>
              </ion-button>
              <ion-button *ngIf="task.MONITOR_HEADER.INCIDENT_TYPE != 'ZON'"
                (click)="updateIncident(task.MONITOR_HEADER)">
                <ion-icon name="create-outline" style="font-size: 30px;">
                </ion-icon>
              </ion-button>
            </ion-buttons>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>

  <!-- PrimeNg Table view for Mobile -->

  <div *ngIf="platform != 'desktop'">
    <p-table #taskTable [value]="filteredIncidentData" [paginator]="true" [rows]="50" responsiveLayout="scroll"
      [paginatorPosition]="'top'" [scrollHeight]="'100%'" [globalFilterFields]="['MONITOR_HEADER.INCIDENT_NO','MONITOR_HEADER.INCIDENT_TYPE','MONITOR_HEADER.INCIDENT_DATE','MONITOR_HEADER.WORKAREA',
    'MONITOR_HEADER.REPORTED_BY','MONITOR_HEADER.DETAIL_DESC','MONITOR_HEADER.EXECUTION_DATE']">

      <ng-template pTemplate="caption">
        <button pButton label="{{'Clear' | translate}}" class="p-button-outlined" icon="pi pi-filter-slash"
          (click)="clear(taskTable)"></button>

        <span class="p-input-icon-left search-float-right">
          <i class="pi pi-search"></i>
          <input pInputText class="search" type="text" (input)="taskTable.filterGlobal($any($event.target).value, 'contains')"
            placeholder="{{'Search' | translate}}" />
        </span>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="MONITOR_HEADER.INCIDENT_NO">{{"I/A no" | translate}}<p-sortIcon
              field="MONITOR_HEADER.INCIDENT_NO"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.INCIDENT_TYPE">{{"Incident Type" | translate}}<p-sortIcon
              field="MONITOR_HEADER.INCIDENT_TYPE"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.INCIDENT_DATE">{{"Event Date" | translate}}<p-sortIcon
              field="MONITOR_HEADER.INCIDENT_DATE"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.WORKAREA">{{"Workarea" | translate}}<p-sortIcon
              field="MONITOR_HEADER.WORKAREA"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.WORKAREA">{{"Accident Location" | translate}}<p-sortIcon
              field="MONITOR_HEADER.WORKAREA"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.REPORTED_BY">{{"Reported By" | translate}}<p-sortIcon
              field="MONITOR_HEADER.REPORTED_BY"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.DETAIL_DESC">{{"Short Description" | translate}}<p-sortIcon
              field="MONITOR_HEADER.DETAIL_DESC"></p-sortIcon>
          </th>
          <th pSortableColumn="MONITOR_HEADER.EXECUTION_DATE">{{"Execution Date" | translate}}<p-sortIcon
              field="MONITOR_HEADER.EXECUTION_DATE"></p-sortIcon>
          </th>
          <th>{{"Update" | translate}}</th>
        </tr>
        <tr>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.INCIDENT_NO" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.INCIDENT_TYPE" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.INCIDENT_DATE" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.WORKAREA" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.AC_LOC_DESC" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.REPORTED_BY" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.DETAIL_DESC" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th><p-columnFilter type="text" field="MONITOR_HEADER.EXECUTION_DATE" [matchMode]="'contains'"
              [matchModeOptions]="matchModeOptions" display="row"
              placeholder="{{'Search' | translate}}"></p-columnFilter>
          </th>
          <th></th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-task>
        <tr>
          <td>{{task.MONITOR_HEADER.INCIDENT_NO}}
            <span *ngIf="task.ATTACHMENTS != null && task.ATTACHMENTS.length > 0" (click)="attachmentIocnClicked(task)" class="pi attachment-icon pi-paperclip"></span>
          </td>
          <td>{{task.MONITOR_HEADER.INCIDENT_TYPE}}</td>
          <td>{{task.MONITOR_HEADER.INCIDENT_DATE | date: 'd MMM, yyyy' }}</td>
          <td>{{task.MONITOR_HEADER.WORKAREA}}</td>
          <td>{{task.MONITOR_HEADER.AC_LOC_DESC}}</td>
          <td>{{task.MONITOR_HEADER.REPORTED_BY}}</td>
          <td>{{task.MONITOR_HEADER.DETAIL_DESC}}</td>
          <td>{{task.MONITOR_HEADER.EXECUTION_DATE | date: 'd MMM, yyyy'}}</td>
          <td>
            <ion-buttons>
              <ion-button disabled="true" *ngIf="task.MONITOR_HEADER.INCIDENT_TYPE == 'ZON'"
                (click)="updateIncident(task.MONITOR_HEADER)">
                <ion-icon name="create-outline" style="font-size: 30px;">
                </ion-icon>
              </ion-button>
              <ion-button *ngIf="task.MONITOR_HEADER.INCIDENT_TYPE != 'ZON'"
                (click)="updateIncident(task.MONITOR_HEADER)">
                <ion-icon name="create-outline" style="font-size: 30px;">
                </ion-icon>
              </ion-button>
            </ion-buttons>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>

  <!-- <div class="container">
    <table>
      <thead>
        <tr>
          <th style="width:5%">{{"Plant" | translate}}</th>
          <th>{{"I/A no" | translate}}</th>
          <th>{{"Incident Type" | translate}}</th>
          <th>{{"Event Date" | translate}}</th>
          <th>{{"Workarea" | translate}}</th>
          <th>{{"Incident Location" | translate}}</th>
          <th>{{"Employee Name" | translate}}</th>
          <th>{{"Reported By" | translate}}</th>
          <th>{{"Execution Date" | translate}}</th>
          <th>{{"Update" | translate}}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of filteredIncidentData" onclick="on">
          <td style="width:5%">{{item.MONITOR_HEADER.PLANT}}</td>
          <td>{{item.MONITOR_HEADER.INCIDENT_NO}}</td>
          <td>{{item.MONITOR_HEADER.INCIDENT_TYPE}}</td>
          <td>{{item.MONITOR_HEADER.INCIDENT_DATE | date: 'd MMM, yyyy' }}</td>
          <td>{{item.MONITOR_HEADER.WORKAREA}}</td>
          <td>{{item.MONITOR_HEADER.AC_LOC_DESC}}</td>
          <td>{{item.MONITOR_HEADER.EMP_NAME}}</td>
          <td>{{item.MONITOR_HEADER.REPORTED_BY}}</td>
          <td>{{item.MONITOR_HEADER.EXECUTION_DATE | date: 'd MMM, yyyy'}}</td>
          <td>
            <ion-buttons>
              <ion-button (click)="updateIncident(item.MONITOR_HEADER)">
                <ion-icon name="create-outline" style="font-size: 30px;">
                </ion-icon>
              </ion-button>
            </ion-buttons>
          </td>
        </tr>
      </tbody>
    </table>
  </div> -->
</ion-content>