import { Component, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, UnviredResult } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertController, ModalController } from '@ionic/angular';
import { DataService } from '../services/data.service';
import { TranslateService } from '@ngx-translate/core';
import { LogMonitorModalComponent } from '../components/log-monitor-modal/log-monitor-modal.component';
import { Table } from 'primeng/table';
import { SelectItem } from 'primeng/api';
import { Platform } from '@ionic/angular';
import { LogMonitorAttachmentsPage } from '../log-monitor-attachments/log-monitor-attachments.page';


@Component({
  selector: 'app-incident-log-monitor',
  templateUrl: './incident-log-monitor.page.html',
  styleUrls: ['./incident-log-monitor.page.scss'],
})
export class IncidentLogMonitorPage {
  public incidentData: any = [];
  public incidentDataRes: any = [];
  public filteredIncidentData: any = [];
  public userFullname = [];
  matchModeOptions: SelectItem[] = [
    { label: 'Contains', value: 'contains' }
  ];
  public platform: string;

  constructor(
    public dataService: DataService,
    public alertController: AlertController,
    public modalController: ModalController,
    private ngZone: NgZone,
    public router: Router,
    public unviredSdk: UnviredCordovaSDK,
    private translate: TranslateService,
    private plt: Platform
  ) { }

  ngOnInit() {
  }

  async ionViewDidEnter() {
    console.log("ionViewDidEnter called");
    let checkPlatform = this.plt.platforms();
    console.log("Platform information", checkPlatform);
    if (checkPlatform.includes("desktop")) {
      this.platform = "desktop"
    } else {
      this.platform = "nonDesktop"
    }
    console.log("current platform", this.platform);
    this.userLable();
    await this.pleaseWaitLoader();
    try {
      let isCredentialsRequriedResult: UnviredResult =
        await this.unviredSdk.isClientCredentialsSet();
      if (
        isCredentialsRequriedResult &&
        isCredentialsRequriedResult.data === false
      ) {
        let inputdata = JSON.parse(localStorage.getItem('querydata'));
        let tempDetails = localStorage.getItem('clientDetails');
        if (tempDetails != null && tempDetails != null) {
          let userDetails = JSON.parse(atob(tempDetails));
          await this.unviredSdk.setClientCredentials(userDetails)
          await this.dataService.getProfile(inputdata);
          this.dataService.setProfileCallIsDone(true);
          this.dataService.setFirstTimeFlag(false);
          await this.getIncidentData();
        }
      } else {
        await this.getIncidentData();
        await this.dismissLoadingController();
      }
    } catch (error) {
      await this.dismissLoadingController();
      await this.dataService.displayAlert(error)
    }
  }

  async getIncidentData() {
    this.incidentData = await this.dataService.getIncidents();
    this.incidentDataRes = []
    this.incidentData.data.MONITOR.forEach(element => {
      this.incidentDataRes.push(element);
    });
    this.incidentData = this.incidentData.data.MONITOR
    this.filteredIncidentData = this.incidentDataRes
    console.log("incidentDataRes", this.incidentDataRes);
  }

  goBackToLastLocation() {
    this.ngZone.run(() => this.router.navigate(['home']));
  }

  handleChange(event) {
    const searchQuery = event.target.value.toLowerCase();
    if (searchQuery.length == 0) {
      this.filteredIncidentData = this.incidentDataRes
    }
    else {
      this.filteredIncidentData = this.incidentDataRes.filter(element =>
        (element.MONITOR_HEADER.EMP_NAME != null && element.MONITOR_HEADER.EMP_NAME.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.PLANT != null && element.MONITOR_HEADER.PLANT.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.INCIDENT_NO != null && element.MONITOR_HEADER.INCIDENT_NO.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.INCIDENT_TYPE != null && element.MONITOR_HEADER.INCIDENT_TYPE.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.INCIDENT_DATE != null && element.MONITOR_HEADER.INCIDENT_DATE.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.WORKAREA != null && element.MONITOR_HEADER.WORKAREA.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.AC_LOC != null && element.MONITOR_HEADER.AC_LOC.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.AC_LOC_DESC != null && element.MONITOR_HEADER.AC_LOC_DESC.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.REPORTED_BY != null && element.MONITOR_HEADER.REPORTED_BY.toString().toLowerCase().includes(searchQuery)) ||
        (element.MONITOR_HEADER.EXECUTION_DATE != null && element.MONITOR_HEADER.EXECUTION_DATE.toString().toLowerCase().includes(searchQuery))
      )
    }
  }

  async updateIncident(item: any) {
    const modal = await this.modalController.create({
      component: LogMonitorModalComponent,
      cssClass: "my-modal",
      componentProps: {
        selectedIncidentData: item
      },
    });
    await modal.present();
    const res = await modal.onDidDismiss();
    if (res.data["updated"]) {
      this.getIncidentData();
    }
  }

  async pleaseWaitLoader() {
    await this.dataService.pleaseWaitLoader();
  }

  async dismissLoadingController() {
    await this.dataService.dismissLoadingController();
  }

  clear(table: Table) {
    table.clear();
  }

  async userLable() {
    let user = await this.dataService.getData('USER_DETAILS_HEADER');
    if (user.length > 0) {
      this.userFullname = user;
    } else {
      console.log("no data found");
    }
  }

  async attachmentIocnClicked(item : any){
    // alert("Attachment icon clickded");
    const modal = await this.modalController.create({
      component: LogMonitorAttachmentsPage,
      cssClass: "my-modal",
      componentProps: {
        selectedIncidentData: item
      },
    });
    await modal.present();
    const res = await modal.onDidDismiss();
  }

  async ionViewDidLeave() {
    console.log("ionViewDidLeave called")
    await this.dismissLoadingController();
  }
}
