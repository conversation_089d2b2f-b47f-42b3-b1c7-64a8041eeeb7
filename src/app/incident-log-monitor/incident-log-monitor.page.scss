.container {
    width: 100%;
}

table {
	width: 100%;
	box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

th{
	padding: 12px;
	background-color: rgba(255,255,255,0.2);
	color: black;
}
td {
	padding: 12px;
	color: black;
}

th {
	text-align: left;
}

thead {
	th {
        top: 0;
        position: sticky;
		background-color: gainsboro;
	}
}

tbody {
	tr {
		&:hover {
			background-color: #EAFDFC;
		}
	}
}

.search-float-right{
	float: right !important;
  }

  .user-title{
    float: center;
    margin-left: 35%;
    font-size: 16px;
    font-weight:100;
  }
  
  .header-title{
    display:flex;
    justify-content: space-around;
  }

  .attachment-icon{
    margin-left: 10%;
  }

  .attachment-icon:hover {
    cursor: pointer;
  }

  .search{
    width: 170px;
  }

  @media only screen and (max-width: 425px){
    .login {
        display: none !important;
    }
  }

@media only screen and (max-width: 425px){
    .mobile-responsive-logo {
        display: none !important;
    }
}

@media only screen and (max-width: 425px){
  .responsive-title {
      font-size: 15px !important;
  }
}


