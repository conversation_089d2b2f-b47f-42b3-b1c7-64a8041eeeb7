import { Component, NgZone } from '@angular/core';
import { NavController, Platform } from '@ionic/angular';
import {
  UnviredCordovaSDK,
  LoginParameters,
  LoginType,
  LoginListenerType,
  LoginResult,
  UnviredResult,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from './services/data.service';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { AppConstants } from './Constants/app-constants';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})

export class AppComponent {
  public params: NavigationExtras;
  private platformState: string = ''
  idleTime: number = 480;

  constructor(
    public platform: Platform,
    public unviredSdk: UnviredCordovaSDK,
    public dataService: DataService,
    private router: Router,
    private ngZone: NgZone,
    private navCtrl: NavController,
    private route: ActivatedRoute,
    public appConstants: AppConstants
  ) {

  }

  async ngOnInit() {
    this.checkScreenTime()
    this.platformState = await this.platform.ready();
    if (this.platformState.length > 0) {
      await this.initializeApp();
      this.dataService.initializeIdleWatcher(this.idleTime, 120);
      this.dataService.getLogoutStatus().subscribe(async (status: boolean) => {
        if (status) {
          localStorage.clear();
          await this.dataService.clearData();
          this.ngZone.run(() => {
            const dataToSend: NavigationExtras = {
              queryParams: {
                type: 0,
              },
            };
            this.navCtrl.navigateBack(['login'], dataToSend);
          });
        }
      });
    }
  }

  checkScreenTime() {
    const expiryTime = parseInt(localStorage.getItem('ng2Idle.main.expiry'), 10);
    const currentTime = Date.now();
    const timeDifference = currentTime - expiryTime;
    if (timeDifference > 0) {
      this.dataService.logout();
    }
  }

  async initializeApp() {
    let loginParameters = new LoginParameters();
    loginParameters.appName = 'EHS';
    loginParameters.url = this.dataService.getUMPUrl();
    loginParameters.company = 'PLASTIPAK';
    loginParameters.loginType = LoginType.unvired;
    loginParameters.domain = 'PLASTIPAK';
    loginParameters.jwtOptions = { app: 'EHS' };
    loginParameters.metadataPath = 'assets/metadata.json';
    loginParameters['appVersion'] = this.appConstants.APP_RELEASE_NUMBER;

    let result: LoginResult;
    try {
      result = await this.unviredSdk.login(loginParameters);
      // this.route.queryParams.subscribe(async (params) => {
      //   console.log("params" + JSON.stringify(params));
      //   if (params && params.Instance) {
      //     let inputDataNew = { SYSID: params.Instance };
      //     let inputdataOld = JSON.parse(localStorage.getItem('querydata'));
      //     if (!inputdataOld || inputdataOld == null) {
      //       localStorage.setItem('querydata', JSON.stringify(inputDataNew));
      //     }
      //     else if (inputDataNew.SYSID == inputdataOld.SYSID) {
      //       return
      //     } else {
      //       await this.dataService.logout();
      //       localStorage.setItem('querydata', JSON.stringify(inputDataNew));
      //     }
      //   }
      // });
      localStorage.setItem('resultType', String(result.type))
      switch (result.type) {
        case LoginListenerType.auth_activation_required:
          this.ngZone.run(() => this.router.navigate(['login']));
          break;
        case LoginListenerType.app_requires_login:
          this.ngZone.run(() => this.router.navigate(['login']));
          break;
        case LoginListenerType.login_success:
          let tempDetails = localStorage.getItem('clientDetails');
          if (tempDetails != null && tempDetails != null) {
            let userDetails = JSON.parse(atob(tempDetails));
            await this.unviredSdk.setClientCredentials(userDetails);
            // this.dataService.restartIdleTimer();
            this.ngZone.run(() => this.router.navigate(['home']));
          } else {
            localStorage.clear();
            this.unviredSdk.logout();
            this.ngZone.run(() => this.router.navigate(['login']));
          }
          break;
      }
    } catch (error) {
      this.unviredSdk.logError(
        'AppComponent',
        'initializeApp',
        'ERROR: ' + error
      );
    }
    this.platform.backButton.subscribeWithPriority(10, () => {
      console.log('Handler was called!');
    });
  }
}
