/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";

// Removing button default text capitalization.
ion-button {
    text-transform: none !important;
    --background: 
  }
  
  ion-segment-button {
    text-transform: none !important;
    font-weight: 520 !important;
  }
  
  .form-conrol-input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }
  
  .driver-info-modal .modal-wrapper {
    height: 100% !important;
    width: 100% !important;
    position: absolute !important;
    display: block !important;
  }
  
  .audit-actions-modal .modal-wrapper {
    width: 50%;
    min-height: 530px;
  }
  
  .sc-ion-modal-ios-h {
    --backdrop-opacity: var(--ion-backdrop-opacity, 0.8);
  }
  .sc-ion-modal-md-h {
    --backdrop-opacity: var(--ion-backdrop-opacity, 0.8);
  }
  
  @media only screen and (min-width: 768px) and (min-height: 600px) {
    .sc-ion-modal-md-h {
      --width: 100%;
      --height: 100%;
    }
  }
  
  ion-segment {
    ion-segment-button {
      border: 1px solid rgb(0, 118, 255);
      border-radius: 8px 8px 8px 8px;
      height: 34px;
      --background-checked: var(--ion-color-primary);
      min-height: 15px !important;
      text-transform: none;
      color: rgb(0, 118, 255);
      --color-checked: transparent;
      --color-focused: transparent;
      --indicator-color: transparent;
      --indicator-color: transparent;
    }
    .segment-active:hover {
      color: #fff;
      ion-label {
        color: #fff;
      }
    }
  }
  
  .bd-left {
    border-radius: 4px 0px 0px 4px !important;
  }
  
  .bd-right {
    border-radius: 0px 1px 1px 0px !important;
  }
  
  .bd-right-last {
    border-radius: 0px 4px 4px 0px !important;
  }
  
  .segment-active {
    background-color: var(--ion-color-primary);
    color: #fff !important;
    --color-hover: #fff;
  }
  
  .segment-active:hover {
    color: #fff !important;
  }
  
  .segment-deactive {
    background-color: #fff !important;
    color: var(--ion-color-primary);
  }
  
  .segment-deactive:hover {
    background-color: #fff !important;
    color: var(--ion-color-primary);
  }
  
  ion-content {
    --background: whitesmoke !important;
  }
  
  .searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios {
    border: 1px solid #ced4da !important;
    border-radius: 0.25rem !important;
  }
  
  .input-group-text {
    width: 135px !important;
  }
  .errorMessage-alignment {
      margin-top: 10px;
      text-align: left !important;
      font-size: 20px !important;
      margin-left: 20px;
  }
  .close-button{
      font-size: 20px;
      float: right;
      margin-right: 10px;
      cursor: pointer;
  }
  .text-danger{
    color: red;
}
.toolbar-bg {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);

  ion-icon {
    color: var(--ion-color-primary-contrast);
  }
}
.outer-container{
  width: 80% !important;
  text-align: center;
  display: block;
  margin-left: auto;
  margin-right: auto;
  margin-top: 30px;
  }
  .my-custom-class {
    --width:600px !important;
  }
  .searchbar-input {
    // padding-left: 9px !important;
    -webkit-padding-end: 5px !important;
    text-transform: uppercase !important;
  }

  // .log-search > .searchbar-input-container > .searchbar-input{ 
  //   padding-left: 0 !important;
  // }

  ::-webkit-input-placeholder { /* WebKit browsers */
    text-transform: none !important; 
}
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    text-transform: none !important;
}
::-moz-placeholder { /* Mozilla Firefox 19+ */
    text-transform: none !important;
}
:-ms-input-placeholder { /* Internet Explorer 10+ */
    text-transform: none !important;
}
::placeholder { /* Recent browsers */
    text-transform: none !important;
}
.alert-wrapper {
  --min-width: 320px !important;
  }

  .alert-button-group{
    background-color: var(--ion-color-base) !important;
  }
  .sc-ion-alert-md-h{
    --min-width: 320px !important;
  }
  .alert-head{
    text-align: center;
    background-color: var(--ion-color-base) !important;

  }

  select:invalid { color:grey; }
  select:valid { color:black; }

  .action-sheet-cancel.sc-ion-action-sheet-ios {
    display: none !important;
  }

  .Action-sheet-center{
    position: absolute;
  left: 50%;
  top: 50%;
  }

  .my-modal {
    --height: 55% !important;
  }